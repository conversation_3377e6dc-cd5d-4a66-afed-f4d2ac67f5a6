package oop2.project.controller;

import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.control.Label;
import javafx.scene.layout.FlowPane;
import javafx.scene.layout.StackPane;
import oop2.project.Main;
import oop2.project.model.Movie;
import oop2.project.service.FavoriteMoviesService;
import oop2.project.service.TmdbService;
import org.tinylog.Logger;

import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * Controller for the hearted/favorite movies view.
 * Displays all movies that the user has marked as favorites.
 */
public class HeartedMoviesController {

    @FXML
    private StackPane contentContainer;

    @FXML
    private FlowPane movieGridContainer;

    @FXML
    private Label emptyStateLabel;

    @FXML
    private Label titleLabel;

    private final FavoriteMoviesService favoriteMoviesService;
    private final TmdbService tmdbService;

    /**
     * Constructor with dependency injection.
     */
    public HeartedMoviesController() {
        this.favoriteMoviesService = FavoriteMoviesService.getInstance();
        this.tmdbService = new TmdbService();
    }

    @FXML
    public void initialize() {
        setupUI();
        loadFavoriteMovies();
    }

    /**
     * Sets up the UI components.
     */
    private void setupUI() {
        // Set title
        if (titleLabel != null) {
            titleLabel.setText("Your Favorite Movies");
        }

        // Configure the grid container
        if (movieGridContainer != null) {
            movieGridContainer.setHgap(20);
            movieGridContainer.setVgap(20);
        }
    }

    /**
     * Loads and displays all favorite movies.
     */
    private void loadFavoriteMovies() {
        Set<Integer> favoriteIds = favoriteMoviesService.getFavoriteMovieIds();
        
        if (favoriteIds.isEmpty()) {
            showEmptyState();
            return;
        }

        showLoadingState();

        // Load movies asynchronously
        List<CompletableFuture<Movie>> movieFutures = favoriteIds.stream()
                .map(tmdbService::getMovieById)
                .toList();

        // Combine all futures
        CompletableFuture<Void> allMovies = CompletableFuture.allOf(
                movieFutures.toArray(new CompletableFuture[0])
        );

        allMovies.thenRun(() -> {
            // Collect all successfully loaded movies
            List<Movie> movies = movieFutures.stream()
                    .map(CompletableFuture::join)
                    .filter(movie -> movie != null)
                    .toList();

            Platform.runLater(() -> {
                if (movies.isEmpty()) {
                    showEmptyState();
                } else {
                    displayMovies(movies);
                    showContentState();
                }
            });
        }).exceptionally(throwable -> {
            Platform.runLater(() -> {
                Logger.error(throwable, "Error loading favorite movies");
                showErrorState();
            });
            return null;
        });
    }

    /**
     * Displays movies in the grid container.
     *
     * @param movies the list of movies to display
     */
    private void displayMovies(List<Movie> movies) {
        movieGridContainer.getChildren().clear();

        // Calculate optimal card width based on available space
        double optimalCardWidth = calculateOptimalCardWidth();

        // Create movie cards
        movies.stream()
                .filter(movie -> movie != null)
                .map(movie -> createMovieCard(movie, optimalCardWidth))
                .filter(node -> node != null)
                .forEach(movieGridContainer.getChildren()::add);
    }

    /**
     * Creates a movie card for the given movie.
     *
     * @param movie the movie to create a card for
     * @param cardWidth the desired width for the card
     * @return the movie card node or null if creation failed
     */
    private Node createMovieCard(Movie movie, double cardWidth) {
        try {
            FXMLLoader loader = new FXMLLoader(Main.class.getResource("view/MovieCard.fxml"));
            Node movieCard = loader.load();

            // Get the controller and set the movie
            MovieCardController controller = loader.getController();
            controller.setMovie(movie);
            controller.setCardWidth(cardWidth);

            // Set callback to refresh the view when favorite status changes
            controller.setOnFavoriteChangedCallback(this::refreshFavoriteMovies);

            return movieCard;
        } catch (IOException e) {
            Logger.error(e, "Failed to create movie card for: {}", movie.getTitle());
            return null;
        }
    }

    /**
     * Calculates the optimal card width based on available space.
     *
     * @return the optimal card width
     */
    private double calculateOptimalCardWidth() {
        if (movieGridContainer == null) {
            return 280; // Default width
        }

        double containerWidth = movieGridContainer.getWidth();
        if (containerWidth <= 0) {
            containerWidth = 1200; // Default container width
        }

        double cardWidth = 280; // Base card width
        double gap = 20; // Gap between cards
        int minCards = 2; // Minimum cards per row
        int maxCards = 6; // Maximum cards per row

        // Calculate how many cards can fit
        for (int cardCount = maxCards; cardCount >= minCards; cardCount--) {
            double totalGapWidth = (cardCount - 1) * gap;
            double availableWidth = containerWidth - totalGapWidth;
            double calculatedCardWidth = availableWidth / cardCount;

            if (calculatedCardWidth >= 240) { // Minimum acceptable card width
                return Math.min(calculatedCardWidth, 320); // Maximum card width
            }
        }

        return cardWidth; // Fallback to default
    }

    /**
     * Shows the loading state.
     */
    private void showLoadingState() {
        if (emptyStateLabel != null) {
            emptyStateLabel.setText("Loading your favorite movies...");
            emptyStateLabel.setVisible(true);
        }
        if (movieGridContainer != null) {
            movieGridContainer.setVisible(false);
        }
    }

    /**
     * Shows the content state (movies are displayed).
     */
    private void showContentState() {
        if (emptyStateLabel != null) {
            emptyStateLabel.setVisible(false);
        }
        if (movieGridContainer != null) {
            movieGridContainer.setVisible(true);
        }
    }

    /**
     * Shows the empty state when no favorite movies are found.
     */
    private void showEmptyState() {
        if (emptyStateLabel != null) {
            emptyStateLabel.setText("You haven't added any movies to your favorites yet.\nStart exploring and click the heart button on movies you love!");
            emptyStateLabel.setVisible(true);
        }
        if (movieGridContainer != null) {
            movieGridContainer.setVisible(false);
        }
    }

    /**
     * Shows the error state when loading fails.
     */
    private void showErrorState() {
        if (emptyStateLabel != null) {
            emptyStateLabel.setText("Failed to load your favorite movies.\nPlease check your internet connection and try again.");
            emptyStateLabel.setVisible(true);
        }
        if (movieGridContainer != null) {
            movieGridContainer.setVisible(false);
        }
    }

    /**
     * Refreshes the favorite movies display.
     * This can be called when movies are added or removed from favorites.
     */
    public void refreshFavoriteMovies() {
        loadFavoriteMovies();
    }
}
