package oop2.project.model;

/**
 * Enum representing the available sorting options for TMDB movie discovery.
 * These correspond to the sort_by parameter values supported by the TMDB API.
 */
public enum SortOption {
    POPULARITY_DESC("popularity.desc", "Popularity (High to Low)"),
    POPULARITY_ASC("popularity.asc", "Popularity (Low to High)"),
    TITLE_ASC("title.asc", "Title (A to Z)"),
    TITLE_DESC("title.desc", "Title (Z to A)"),
    ORIGINAL_TITLE_ASC("original_title.asc", "Original Title (A to Z)"),
    ORIGINAL_TITLE_DESC("original_title.desc", "Original Title (Z to A)"),
    RELEASE_DATE_DESC("primary_release_date.desc", "Release Date (Newest First)"),
    RELEASE_DATE_ASC("primary_release_date.asc", "Release Date (Oldest First)"),
    VOTE_AVERAGE_DESC("vote_average.desc", "Rating (High to Low)"),
    VOTE_AVERAGE_ASC("vote_average.asc", "Rating (Low to High)"),
    VOTE_COUNT_DESC("vote_count.desc", "Vote Count (High to Low)"),
    VOTE_COUNT_ASC("vote_count.asc", "Vote Count (Low to High)"),
    REVENUE_DESC("revenue.desc", "Revenue (High to Low)"),
    REVENUE_ASC("revenue.asc", "Revenue (Low to High)");

    private final String apiValue;
    private final String displayName;

    SortOption(String apiValue, String displayName) {
        this.apiValue = apiValue;
        this.displayName = displayName;
    }

    /**
     * Gets the API parameter value for this sort option.
     *
     * @return the sort_by parameter value for the TMDB API
     */
    public String getApiValue() {
        return apiValue;
    }

    /**
     * Gets the human-readable display name for this sort option.
     *
     * @return the display name to show in the UI
     */
    public String getDisplayName() {
        return displayName;
    }

    @Override
    public String toString() {
        return displayName;
    }

    /**
     * Gets the default sort option.
     *
     * @return the default sort option (popularity descending)
     */
    public static SortOption getDefault() {
        return POPULARITY_DESC;
    }
}
