package oop2.project.service;

import oop2.project.config.AppConfig;
import oop2.project.model.Language;
import oop2.project.model.MovieSearcherState;
import oop2.project.model.SortOption;
import org.tinylog.Logger;

/**
 * Service for managing the state of the movie searcher view.
 * This singleton service preserves the search state when users navigate between views,
 * allowing them to return to their previous search results and filters.
 * 
 * Uses the Memento pattern to capture and restore view state.
 */
public class MovieSearcherStateService {
    
    private static final MovieSearcherStateService instance = new MovieSearcherStateService();
    
    private MovieSearcherState currentState;
    private final AppConfig appConfig;
    
    private MovieSearcherStateService() {
        this.appConfig = AppConfig.getInstance();
        this.currentState = createInitialState();
    }
    
    public static MovieSearcherStateService getInstance() {
        return instance;
    }
    
    /**
     * Creates an initial state with default values from AppConfig
     */
    private MovieSearcherState createInitialState() {
        MovieSearcherState state = new MovieSearcherState();
        
        // Initialize with values from AppConfig
        state.setSelectedLanguage(appConfig.getLanguage());
        state.setMinimumRating(appConfig.getMinimumRating());
        state.setIncludeAdultContent(appConfig.allowAdultContent());
        state.setSelectedSortOption(SortOption.getDefault());
        
        Logger.debug("Created initial movie searcher state: {}", state);
        return state;
    }
    
    /**
     * Saves the current state of the movie searcher.
     * This should be called when the user navigates away from the searcher view.
     * 
     * @param state the current state to save
     */
    public void saveState(MovieSearcherState state) {
        if (state != null) {
            // Create a deep copy to avoid reference issues
            this.currentState = new MovieSearcherState(state);
            Logger.debug("Saved movie searcher state: {}", this.currentState);
        }
    }
    
    /**
     * Restores the previously saved state.
     * This should be called when the user navigates back to the searcher view.
     * 
     * @return a copy of the saved state, or a fresh initial state if none exists
     */
    public MovieSearcherState restoreState() {
        if (currentState != null) {
            Logger.debug("Restoring movie searcher state: {}", currentState);
            return new MovieSearcherState(currentState);
        } else {
            Logger.debug("No saved state found, creating initial state");
            return createInitialState();
        }
    }
    
    /**
     * Checks if there is a saved state available
     * 
     * @return true if a state has been saved, false otherwise
     */
    public boolean hasSavedState() {
        return currentState != null && currentState.hasPerformedSearch();
    }
    
    /**
     * Clears the saved state, effectively resetting to initial conditions.
     * This might be useful when user settings change significantly.
     */
    public void clearState() {
        this.currentState = createInitialState();
        Logger.debug("Cleared movie searcher state, reset to initial");
    }
    
    /**
     * Updates the saved state with current AppConfig values.
     * This should be called when app configuration changes to ensure
     * the state reflects the new preferences.
     */
    public void updateStateFromConfig() {
        if (currentState != null) {
            // Update filter defaults from config, but preserve user's current search
            Language configLanguage = appConfig.getLanguage();
            double configRating = appConfig.getMinimumRating();
            boolean configAdult = appConfig.allowAdultContent();
            
            // Only update if the user hasn't customized these values
            // (This is a design choice - you might want to always update from config)
            if (currentState.getSelectedLanguage() == null) {
                currentState.setSelectedLanguage(configLanguage);
            }
            
            Logger.debug("Updated state from config: language={}, rating={}, adult={}", 
                        configLanguage, configRating, configAdult);
        }
    }
    
    /**
     * Gets the current state without removing it (peek operation)
     * 
     * @return a copy of the current state, or null if none exists
     */
    public MovieSearcherState getCurrentState() {
        return currentState != null ? new MovieSearcherState(currentState) : null;
    }
    
    /**
     * Checks if the current state represents a meaningful search session
     * (i.e., user has performed searches and has results to return to)
     * 
     * @return true if the state contains meaningful search data
     */
    public boolean hasActiveSearchSession() {
        return currentState != null && 
               currentState.hasPerformedSearch() && 
               (currentState.getLastSearchResult() != null || 
                !currentState.getSearchText().trim().isEmpty());
    }
    
    /**
     * Resets only the search results while preserving filter settings.
     * Useful when you want to keep user's filter preferences but start fresh.
     */
    public void resetSearchResults() {
        if (currentState != null) {
            currentState.setSearchText("");
            currentState.setCurrentPage(1);
            currentState.setLastSearchResult(null);
            currentState.setHasPerformedSearch(false);
            currentState.setCurrentViewState(MovieSearcherState.ViewState.INITIAL);
            
            Logger.debug("Reset search results, preserved filter settings");
        }
    }
}
