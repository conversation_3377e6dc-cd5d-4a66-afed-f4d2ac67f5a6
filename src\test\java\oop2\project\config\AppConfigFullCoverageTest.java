package oop2.project.config;

import oop2.project.model.Language;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.*;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive test class for AppConfig achieving 100% coverage using dependency injection.
 */
public class AppConfigFullCoverageTest {

    @Mock
    private AppConfig.FileSystemOperations mockFileSystemOperations;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testSuccessfulConfigLoading() throws Exception {
        // Given - a valid config file content
        String configContent = """
                # Test configuration
                language=de-CH
                allowAdultContent=true
                minimumRating=7.5
                openAiApiKey=test-openai-key
                tmdbApiKey=test-tmdb-key
                """;
        
        InputStream inputStream = new ByteArrayInputStream(configContent.getBytes());
        
        // Mock successful file operations
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenReturn(inputStream);
        
        // When creating AppConfig instance
        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);
        
        // Then the config should be loaded correctly
        assertEquals("de-CH", config.get(AppConfig.KEY_LANGUAGE));
        assertEquals("true", config.get(AppConfig.KEY_ALLOW_ADULT_CONTENT));
        assertEquals("7.5", config.get(AppConfig.KEY_MINIMUM_RATING));
        assertEquals("test-openai-key", config.get(AppConfig.KEY_OPENAI_API_KEY));
        assertEquals("test-tmdb-key", config.get(AppConfig.KEY_TMDB_API_KEY));
    }

    @Test
    public void testConfigLoadingWithMalformedLines() throws Exception {
        // Given - config content with malformed lines
        String configContent = """
                # Test configuration
                language=de-CH
                malformed_line_without_equals
                allowAdultContent=true
                another=malformed=line=with=multiple=equals
                minimumRating=7.5
                """;
        
        InputStream inputStream = new ByteArrayInputStream(configContent.getBytes());
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenReturn(inputStream);
        
        // When creating AppConfig instance
        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);
        
        // Then valid lines should be loaded, malformed lines should be skipped
        assertEquals("de-CH", config.get(AppConfig.KEY_LANGUAGE));
        assertEquals("true", config.get(AppConfig.KEY_ALLOW_ADULT_CONTENT));
        assertEquals("7.5", config.get(AppConfig.KEY_MINIMUM_RATING));
    }

    @Test
    public void testConfigLoadingWithCommentsAndEmptyLines() throws Exception {
        // Given - config content with comments and empty lines
        String configContent = """
                # This is a comment
                
                language=de-CH
                
                # Another comment
                allowAdultContent=true
                
                minimumRating=7.5
                """;
        
        InputStream inputStream = new ByteArrayInputStream(configContent.getBytes());
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenReturn(inputStream);
        
        // When creating AppConfig instance
        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);
        
        // Then only non-comment, non-empty lines should be loaded
        assertEquals("de-CH", config.get(AppConfig.KEY_LANGUAGE));
        assertEquals("true", config.get(AppConfig.KEY_ALLOW_ADULT_CONTENT));
        assertEquals("7.5", config.get(AppConfig.KEY_MINIMUM_RATING));
    }

    @Test
    public void testConfigLoadingIOExceptionTriggersDefaults() throws Exception {
        // Given - file system operations that throw IOException
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenThrow(new IOException("File not found"));
        
        // Mock directory operations for loadAndSaveDefaults
        when(mockFileSystemOperations.directoryExists(any(Path.class))).thenReturn(true);
        
        // Mock save operation
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        when(mockFileSystemOperations.newOutputStream(any(Path.class))).thenReturn(outputStream);
        
        // When creating AppConfig instance
        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);
        
        // Then default values should be used
        assertEquals(AppConfig.DEFAULT_LANGUAGE, config.get(AppConfig.KEY_LANGUAGE));
        assertEquals(String.valueOf(AppConfig.DEFAULT_ALLOW_ADULT_CONTENT), config.get(AppConfig.KEY_ALLOW_ADULT_CONTENT));
        assertEquals(String.valueOf(AppConfig.DEFAULT_MINIMUM_RATING), config.get(AppConfig.KEY_MINIMUM_RATING));
        
        // Verify that save was called
        verify(mockFileSystemOperations).newOutputStream(any(Path.class));
    }

    @Test
    public void testDirectoryCreationWhenDirectoryExists() throws Exception {
        // Given - directory already exists
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenThrow(new IOException("File not found"));
        when(mockFileSystemOperations.directoryExists(any(Path.class))).thenReturn(true);
        
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        when(mockFileSystemOperations.newOutputStream(any(Path.class))).thenReturn(outputStream);
        
        // When creating AppConfig instance
        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);
        
        // Then createDirectories should not be called
        verify(mockFileSystemOperations, never()).createDirectories(any(Path.class));
        assertNotNull(config);
    }

    @Test
    public void testDirectoryCreationSuccess() throws Exception {
        // Given - directory doesn't exist but creation succeeds
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenThrow(new IOException("File not found"));
        when(mockFileSystemOperations.directoryExists(any(Path.class))).thenReturn(false);
        when(mockFileSystemOperations.createDirectories(any(Path.class))).thenReturn(true);
        
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        when(mockFileSystemOperations.newOutputStream(any(Path.class))).thenReturn(outputStream);
        
        // When creating AppConfig instance
        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);
        
        // Then createDirectories should be called and succeed
        verify(mockFileSystemOperations).createDirectories(any(Path.class));
        assertNotNull(config);
    }

    @Test
    public void testDirectoryCreationFailure() throws Exception {
        // Given - directory doesn't exist and creation fails
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenThrow(new IOException("File not found"));
        when(mockFileSystemOperations.directoryExists(any(Path.class))).thenReturn(false);
        when(mockFileSystemOperations.createDirectories(any(Path.class))).thenReturn(false);
        when(mockFileSystemOperations.getAbsolutePath(any(Path.class))).thenReturn("/fake/config/directory");
        
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        when(mockFileSystemOperations.newOutputStream(any(Path.class))).thenReturn(outputStream);
        
        // When creating AppConfig instance
        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);
        
        // Then createDirectories should be called and fail, but config should still be created
        verify(mockFileSystemOperations).createDirectories(any(Path.class));
        verify(mockFileSystemOperations).getAbsolutePath(any(Path.class));
        assertNotNull(config);
    }

    @Test
    public void testSaveWhenNotDirty() throws Exception {
        // Given - a config that is not dirty
        InputStream inputStream = new ByteArrayInputStream("language=en-US\n".getBytes());
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenReturn(inputStream);
        
        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);
        
        // When calling save on a non-dirty config
        config.save();
        
        // Then newOutputStream should not be called
        verify(mockFileSystemOperations, never()).newOutputStream(any(Path.class));
    }

    @Test
    public void testSaveWhenDirty() throws Exception {
        // Given - a config that becomes dirty
        InputStream inputStream = new ByteArrayInputStream("language=en-US\n".getBytes());
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenReturn(inputStream);
        
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        when(mockFileSystemOperations.newOutputStream(any(Path.class))).thenReturn(outputStream);
        
        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);
        
        // Make the config dirty
        config.set("testKey", "testValue");
        
        // When calling save
        config.save();
        
        // Then the config should be saved
        verify(mockFileSystemOperations).newOutputStream(any(Path.class));
        
        String savedContent = outputStream.toString();
        assertTrue(savedContent.contains("# Application Configuration"));
        assertTrue(savedContent.contains("testKey=testValue"));
    }

    @Test
    public void testSaveIOException() throws Exception {
        // Given - save operation that throws IOException
        InputStream inputStream = new ByteArrayInputStream("language=en-US\n".getBytes());
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenReturn(inputStream);
        when(mockFileSystemOperations.newOutputStream(any(Path.class))).thenThrow(new IOException("Write failed"));

        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);

        // Make the config dirty
        config.set("testKey", "testValue");

        // When calling save (should handle IOException gracefully)
        assertDoesNotThrow(() -> config.save());

        // Verify that save was attempted
        verify(mockFileSystemOperations).newOutputStream(any(Path.class));
    }

    @Test
    public void testGenericGetWithDifferentTypes() throws Exception {
        // Given - a config with various values
        InputStream inputStream = new ByteArrayInputStream("""
                stringKey=testValue
                booleanKey=true
                integerKey=42
                floatKey=3.14
                doubleKey=2.718
                """.getBytes());
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenReturn(inputStream);

        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);

        // Test different type conversions
        assertEquals("testValue", config.get("stringKey", String.class));
        assertEquals(Boolean.TRUE, config.get("booleanKey", Boolean.class));
        assertEquals(Integer.valueOf(42), config.get("integerKey", Integer.class));
        assertEquals(Float.valueOf(3.14f), config.get("floatKey", Float.class));
        assertEquals(Double.valueOf(2.718), config.get("doubleKey", Double.class));
    }

    @Test
    public void testGenericGetWithNullValue() throws Exception {
        // Given - a config
        InputStream inputStream = new ByteArrayInputStream("language=en-US\n".getBytes());
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenReturn(inputStream);

        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);

        // When getting a non-existent key
        String result = config.get("nonExistentKey", String.class);

        // Then it should return null
        assertNull(result);
    }

    @Test
    public void testGenericGetWithUnsupportedType() throws Exception {
        // Given - a config
        InputStream inputStream = new ByteArrayInputStream("testKey=testValue\n".getBytes());
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenReturn(inputStream);

        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);

        // When getting with unsupported type
        Object result = config.get("testKey", Object.class);

        // Then it should return null
        assertNull(result);
    }

    @Test
    public void testGenericGetWithInvalidValue() throws Exception {
        // Given - a config with invalid numeric value
        InputStream inputStream = new ByteArrayInputStream("invalidIntKey=notAnInteger\n".getBytes());
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenReturn(inputStream);

        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);

        // When getting as Integer
        Integer result = config.get("invalidIntKey", Integer.class);

        // Then it should return null due to parsing error
        assertNull(result);
    }

    @Test
    public void testLanguageHandling() throws Exception {
        // Given - a config
        InputStream inputStream = new ByteArrayInputStream("language=en-US\n".getBytes());
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenReturn(inputStream);

        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);

        // When setting and getting languages
        config.setLanguage(Language.English);
        assertEquals(Language.English, config.getLanguage());

        config.setLanguage(Language.Deutsch);
        assertEquals(Language.Deutsch, config.getLanguage());
    }

    @Test
    public void testInvalidLanguageCodeHandling() throws Exception {
        // Given - a config with invalid language code
        InputStream inputStream = new ByteArrayInputStream("language=invalid-language-code\n".getBytes());
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenReturn(inputStream);

        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);

        // When getting the language
        Language language = config.getLanguage();

        // Then it should fall back to default language
        assertEquals(Language.English, language);
        assertEquals(AppConfig.DEFAULT_LANGUAGE, language.getLanguageCode());
    }

    @Test
    public void testApiKeyHandling() throws Exception {
        // Given - a config
        InputStream inputStream = new ByteArrayInputStream("language=en-US\n".getBytes());
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenReturn(inputStream);

        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);

        // When setting API keys
        config.setOpenAiApiKey("test-openai-key");
        config.setTmdbApiKey("test-tmdb-key");

        // Then they should be retrievable
        assertEquals("test-openai-key", config.getOpenAiApiKey());
        assertEquals("test-tmdb-key", config.getTmdbApiKey());
    }

    @Test
    public void testConfigValidation() throws Exception {
        // Given - a config
        InputStream inputStream = new ByteArrayInputStream("language=en-US\n".getBytes());
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenReturn(inputStream);

        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);

        // When both keys are set
        config.setOpenAiApiKey("test-openai-key");
        config.setTmdbApiKey("test-tmdb-key");
        assertTrue(config.isValid());

        // When one key is missing
        config.setTmdbApiKey("");
        assertFalse(config.isValid());

        // When both keys are missing
        config.setOpenAiApiKey("");
        assertFalse(config.isValid());
    }

    @Test
    public void testRatingAndAdultContentHandling() throws Exception {
        // Given - a config
        InputStream inputStream = new ByteArrayInputStream("language=en-US\n".getBytes());
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenReturn(inputStream);

        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);

        // When setting rating and adult content
        config.setMinimumRating(7.5);
        config.setAllowAdultContent(true);

        // Then they should be retrievable
        assertEquals(7.5, config.getMinimumRating(), 0.01);
        assertTrue(config.allowAdultContent());

        // When setting to false
        config.setAllowAdultContent(false);
        assertFalse(config.allowAdultContent());
    }

    @Test
    public void testGenericSetAndGet() throws Exception {
        // Given - a config
        InputStream inputStream = new ByteArrayInputStream("language=en-US\n".getBytes());
        when(mockFileSystemOperations.newInputStream(any(Path.class))).thenReturn(inputStream);

        AppConfig config = AppConfig.createTestInstance(mockFileSystemOperations);

        // When setting a custom property
        config.set("customKey", "customValue");

        // Then it should be retrievable
        assertEquals("customValue", config.get("customKey"));
    }

    @Test
    public void testSingletonPattern() {
        // When getting instances
        AppConfig config1 = AppConfig.getInstance();
        AppConfig config2 = AppConfig.getInstance();

        // Then both should be the same instance
        assertSame(config1, config2);
    }

    @Test
    public void testConstants() {
        // Test that all constants are properly defined
        assertEquals("en-US", AppConfig.DEFAULT_LANGUAGE);
        assertFalse(AppConfig.DEFAULT_ALLOW_ADULT_CONTENT);
        assertEquals(-1.0, AppConfig.DEFAULT_MINIMUM_RATING, 0.01);

        assertEquals("language", AppConfig.KEY_LANGUAGE);
        assertEquals("allowAdultContent", AppConfig.KEY_ALLOW_ADULT_CONTENT);
        assertEquals("minimumRating", AppConfig.KEY_MINIMUM_RATING);
        assertEquals("openAiApiKey", AppConfig.KEY_OPENAI_API_KEY);
        assertEquals("tmdbApiKey", AppConfig.KEY_TMDB_API_KEY);
    }
}
