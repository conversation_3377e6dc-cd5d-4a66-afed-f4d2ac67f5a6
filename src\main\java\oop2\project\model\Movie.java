package oop2.project.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.text.NumberFormat;

/**
 * Represents a movie from TMDB API.
 * This class is designed to be deserialized from JSON responses.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Movie {

    private boolean adult;

    @JsonProperty("backdrop_path")
    private String backdropPath;

    @JsonProperty("genre_ids")
    private List<Integer> genreIds = new ArrayList<>();

    private int id;

    @JsonProperty("original_language")
    private String originalLanguage;

    @JsonProperty("original_title")
    private String originalTitle;

    private String overview;

    private double popularity;

    @JsonProperty("poster_path")
    private String posterPath;

    @JsonProperty("release_date")
    private String releaseDate;

    private String title;

    private boolean video;

    @JsonProperty("vote_average")
    private double voteAverage;

    @JsonProperty("vote_count")
    private int voteCount;

    private int runtime;

    private long budget;

    private long revenue;

    private String tagline;

    private String status;

    @JsonProperty("genres")
    private List<Genre> genres = new ArrayList<>();

    @JsonProperty("production_companies")
    private List<ProductionCompany> productionCompanies = new ArrayList<>();

    /**
     * Default constructor required for Jackson deserialization.
     */
    public Movie() {
    }

    /**
     * Represents a movie genre.
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Genre {
        private int id;
        private String name;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    /**
     * Represents a production company.
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ProductionCompany {
        private int id;
        private String name;

        @JsonProperty("logo_path")
        private String logoPath;

        @JsonProperty("origin_country")
        private String originCountry;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getLogoPath() {
            return logoPath;
        }

        public void setLogoPath(String logoPath) {
            this.logoPath = logoPath;
        }

        public String getOriginCountry() {
            return originCountry;
        }

        public void setOriginCountry(String originCountry) {
            this.originCountry = originCountry;
        }
    }

    /**
     * Gets whether the movie is for adults only.
     *
     * @return true if the movie is for adults, false otherwise
     */
    public boolean isAdult() {
        return adult;
    }

    /**
     * Sets whether the movie is for adults only.
     *
     * @param adult true if the movie is for adults, false otherwise
     */
    public void setAdult(boolean adult) {
        this.adult = adult;
    }

    /**
     * Gets the backdrop image path.
     *
     * @return the backdrop image path
     */
    public String getBackdropPath() {
        return backdropPath;
    }

    /**
     * Sets the backdrop image path.
     *
     * @param backdropPath the backdrop image path
     */
    public void setBackdropPath(String backdropPath) {
        this.backdropPath = backdropPath;
    }

    /**
     * Gets the list of genre IDs.
     *
     * @return the list of genre IDs
     */
    public List<Integer> getGenreIds() {
        return genreIds;
    }

    /**
     * Sets the list of genre IDs.
     *
     * @param genreIds the list of genre IDs
     */
    public void setGenreIds(List<Integer> genreIds) {
        this.genreIds = genreIds;
    }

    /**
     * Gets the movie ID.
     *
     * @return the movie ID
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the movie ID.
     *
     * @param id the movie ID
     */
    public void setId(int id) {
        this.id = id;
    }

    /**
     * Gets the original language.
     *
     * @return the original language
     */
    public String getOriginalLanguage() {
        return originalLanguage;
    }

    /**
     * Sets the original language.
     *
     * @param originalLanguage the original language
     */
    public void setOriginalLanguage(String originalLanguage) {
        this.originalLanguage = originalLanguage;
    }

    /**
     * Gets the original title.
     *
     * @return the original title
     */
    public String getOriginalTitle() {
        return originalTitle;
    }

    /**
     * Sets the original title.
     *
     * @param originalTitle the original title
     */
    public void setOriginalTitle(String originalTitle) {
        this.originalTitle = originalTitle;
    }

    /**
     * Gets the overview/description.
     *
     * @return the overview
     */
    public String getOverview() {
        return overview;
    }

    /**
     * Sets the overview/description.
     *
     * @param overview the overview
     */
    public void setOverview(String overview) {
        this.overview = overview;
    }

    /**
     * Gets the popularity score.
     *
     * @return the popularity score
     */
    public double getPopularity() {
        return popularity;
    }

    /**
     * Sets the popularity score.
     *
     * @param popularity the popularity score
     */
    public void setPopularity(double popularity) {
        this.popularity = popularity;
    }

    /**
     * Gets the poster image path.
     *
     * @return the poster image path
     */
    public String getPosterPath() {
        return posterPath;
    }

    /**
     * Sets the poster image path.
     *
     * @param posterPath the poster image path
     */
    public void setPosterPath(String posterPath) {
        this.posterPath = posterPath;
    }

    /**
     * Gets the release date as a string.
     *
     * @return the release date string
     */
    public String getReleaseDate() {
        return releaseDate;
    }

    /**
     * Sets the release date string.
     *
     * @param releaseDate the release date string
     */
    public void setReleaseDate(String releaseDate) {
        this.releaseDate = releaseDate;
    }

    /**
     * Gets the formatted release date.
     *
     * @return the formatted release date or "Unknown" if not available
     */
    public String getFormattedReleaseDate() {
        if (releaseDate == null || releaseDate.isEmpty()) {
            return "Unknown";
        }

        try {
            LocalDate date = LocalDate.parse(releaseDate);
            return date.format(DateTimeFormatter.ofPattern("MMMM d, yyyy"));
        } catch (DateTimeParseException e) {
            return releaseDate;
        }
    }

    /**
     * Gets the release year.
     *
     * @return the release year or null if not available
     */
    public Integer getReleaseYear() {
        if (releaseDate == null || releaseDate.isEmpty()) {
            return null;
        }

        try {
            LocalDate date = LocalDate.parse(releaseDate);
            return date.getYear();
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    /**
     * Gets the title.
     *
     * @return the title
     */
    public String getTitle() {
        return title;
    }

    /**
     * Sets the title.
     *
     * @param title the title
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * Gets whether the movie has video.
     *
     * @return true if the movie has video, false otherwise
     */
    public boolean isVideo() {
        return video;
    }

    /**
     * Sets whether the movie has video.
     *
     * @param video true if the movie has video, false otherwise
     */
    public void setVideo(boolean video) {
        this.video = video;
    }

    /**
     * Gets the vote average/rating.
     *
     * @return the vote average
     */
    public double getVoteAverage() {
        return voteAverage;
    }

    /**
     * Sets the vote average/rating.
     *
     * @param voteAverage the vote average
     */
    public void setVoteAverage(double voteAverage) {
        this.voteAverage = voteAverage;
    }

    /**
     * Gets the vote count.
     *
     * @return the vote count
     */
    public int getVoteCount() {
        return voteCount;
    }

    /**
     * Sets the vote count.
     *
     * @param voteCount the vote count
     */
    public void setVoteCount(int voteCount) {
        this.voteCount = voteCount;
    }

    /**
     * Gets the full poster URL.
     *
     * @return the full poster URL or null if not available
     */
    public String getFullPosterPath() {
        if (posterPath == null || posterPath.isEmpty()) {
            return null;
        }
        return "https://image.tmdb.org/t/p/w500" + posterPath;
    }

    /**
     * Gets the full backdrop URL.
     *
     * @return the full backdrop URL or null if not available
     */
    public String getFullBackdropPath() {
        if (backdropPath == null || backdropPath.isEmpty()) {
            return null;
        }
        return "https://image.tmdb.org/t/p/w1280" + backdropPath;
    }

    /**
     * Gets a formatted rating string.
     *
     * @return the formatted rating string
     */
    public String getFormattedRating() {
        return String.format("%.1f/10", voteAverage);
    }

    /**
     * Gets the runtime in minutes.
     *
     * @return the runtime in minutes
     */
    public int getRuntime() {
        return runtime;
    }

    /**
     * Sets the runtime in minutes.
     *
     * @param runtime the runtime in minutes
     */
    public void setRuntime(int runtime) {
        this.runtime = runtime;
    }

    /**
     * Gets the formatted runtime string (e.g., "2h 15m").
     *
     * @return the formatted runtime string
     */
    public String getFormattedRuntime() {
        if (runtime <= 0) {
            return "Unknown";
        }

        int hours = runtime / 60;
        int minutes = runtime % 60;

        if (hours > 0) {
            return String.format("%dh %dm", hours, minutes);
        } else {
            return String.format("%dm", minutes);
        }
    }

    /**
     * Gets the budget.
     *
     * @return the budget
     */
    public long getBudget() {
        return budget;
    }

    /**
     * Sets the budget.
     *
     * @param budget the budget
     */
    public void setBudget(long budget) {
        this.budget = budget;
    }

    /**
     * Gets the formatted budget string with currency symbol.
     *
     * @return the formatted budget string
     */
    public String getFormattedBudget() {
        if (budget <= 0) {
            return "Unknown";
        }

        // Format without the dollar sign to avoid FXML parsing issues
        return "USD " + NumberFormat.getNumberInstance(Locale.US).format(budget);
    }

    /**
     * Gets the revenue.
     *
     * @return the revenue
     */
    public long getRevenue() {
        return revenue;
    }

    /**
     * Sets the revenue.
     *
     * @param revenue the revenue
     */
    public void setRevenue(long revenue) {
        this.revenue = revenue;
    }

    /**
     * Gets the formatted revenue string with currency symbol.
     *
     * @return the formatted revenue string
     */
    public String getFormattedRevenue() {
        if (revenue <= 0) {
            return "Unknown";
        }

        // Format without the dollar sign to avoid FXML parsing issues
        return "USD " + NumberFormat.getNumberInstance(Locale.US).format(revenue);
    }

    /**
     * Gets the tagline.
     *
     * @return the tagline
     */
    public String getTagline() {
        return tagline;
    }

    /**
     * Sets the tagline.
     *
     * @param tagline the tagline
     */
    public void setTagline(String tagline) {
        this.tagline = tagline;
    }

    /**
     * Gets the status.
     *
     * @return the status
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the status.
     *
     * @param status the status
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * Gets the list of genres.
     *
     * @return the list of genres
     */
    public List<Genre> getGenres() {
        return genres;
    }

    /**
     * Sets the list of genres.
     *
     * @param genres the list of genres
     */
    public void setGenres(List<Genre> genres) {
        this.genres = genres;
    }

    /**
     * Gets a comma-separated string of genre names.
     *
     * @return the genre names string
     */
    public String getGenreNames() {
        if (genres == null || genres.isEmpty()) {
            return "Unknown";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < genres.size(); i++) {
            sb.append(genres.get(i).getName());
            if (i < genres.size() - 1) {
                sb.append(", ");
            }
        }

        return sb.toString();
    }

    /**
     * Gets the list of production companies.
     *
     * @return the list of production companies
     */
    public List<ProductionCompany> getProductionCompanies() {
        return productionCompanies;
    }

    /**
     * Sets the list of production companies.
     *
     * @param productionCompanies the list of production companies
     */
    public void setProductionCompanies(List<ProductionCompany> productionCompanies) {
        this.productionCompanies = productionCompanies;
    }

    /**
     * Gets a comma-separated string of production company names.
     *
     * @return the production company names string
     */
    public String getProductionCompanyNames() {
        if (productionCompanies == null || productionCompanies.isEmpty()) {
            return "Unknown";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < productionCompanies.size(); i++) {
            sb.append(productionCompanies.get(i).getName());
            if (i < productionCompanies.size() - 1) {
                sb.append(", ");
            }
        }

        return sb.toString();
    }
}
