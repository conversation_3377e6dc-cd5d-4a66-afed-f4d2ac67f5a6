<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<?scenebuilder-stylesheet ../css/MovieSearcher.css?>

<VBox xmlns="http://javafx.com/javafx/17.0.12"
      xmlns:fx="http://javafx.com/fxml/1"
      spacing="20.0"
      styleClass="movie-list-container" 
      fx:controller="oop2.project.controller.HeartedMoviesController">
    
    <padding>
        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0"/>
    </padding>

    <!-- Header Section -->
    <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="header-section">
        <Label fx:id="titleLabel" text="Your Favorite Movies" styleClass="page-title" style="-fx-font-size: 18px; -fx-font-weight: bold;"/>
    </HBox>

    <!-- Content Container -->
    <StackPane fx:id="contentContainer" VBox.vgrow="ALWAYS">
        
        <!-- Movie Grid Container -->
        <ScrollPane fitToWidth="true" fitToHeight="true" styleClass="movie-scroll-pane">
            <FlowPane fx:id="movieGridContainer" 
                     alignment="TOP_LEFT"
                     hgap="20.0" 
                     vgap="20.0"
                     styleClass="movie-grid-container">
                <padding>
                    <Insets bottom="20.0" left="20.0" right="20.0" top="20.0"/>
                </padding>
            </FlowPane>
        </ScrollPane>

        <!-- Empty State -->
        <VBox fx:id="emptyStateContainer" 
              alignment="CENTER" 
              spacing="20.0" 
              styleClass="empty-state-container"
              visible="false">
            <FontIcon iconLiteral="far-heart" iconSize="64" styleClass="empty-state-icon"/>
            <Label fx:id="emptyStateLabel" 
                   text="You haven't added any movies to your favorites yet.&#10;Start exploring and click the heart button on movies you love!"
                   styleClass="empty-state-text"
                   textAlignment="CENTER"
                   wrapText="true"/>
        </VBox>
        
    </StackPane>
    
</VBox>
