/* Movie Detail View Styles */

/* Main Container */
.movie-detail-view {
    -fx-background-color: #f8f9fa;
}



/* Content */
.content-scroll {
    -fx-background-color: transparent;
    -fx-background: transparent;
}

.content-container {
    -fx-background-color: white;
    -fx-background-radius: 16;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 15, 0, 0, 4);
}

/* Poster */
.detail-poster-container {
    -fx-background-radius: 12;
    -fx-clip: true;
    -fx-min-width: 300;
    -fx-max-width: 300;
    -fx-min-height: 450;
    -fx-max-height: 450;
}

.detail-movie-poster {
    -fx-background-radius: 12;
    -fx-clip: true;
}

.detail-poster-placeholder {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 12;
    -fx-min-width: 300;
    -fx-max-width: 300;
    -fx-min-height: 450;
    -fx-max-height: 450;
}

.placeholder-icon {
    -fx-icon-color: #bdc3c7;
}

.placeholder-text {
    -fx-font-size: 16px;
    -fx-text-fill: #7f8c8d;
    -fx-font-weight: 500;
}

/* Typography */
.detail-movie-title {
    -fx-font-size: 32px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
    -fx-line-spacing: 4;
}

.detail-movie-year {
    -fx-font-size: 18px;
    -fx-text-fill: #7f8c8d;
    -fx-font-weight: 500;
}

.detail-movie-duration {
    -fx-font-size: 16px;
    -fx-text-fill: #95a5a6;
    -fx-font-weight: 400;
}

.rating-container {
    -fx-background-color: #fff3cd;
    -fx-background-radius: 16;
    -fx-padding: 6 12;
}

.rating-star {
    -fx-icon-color: #f39c12;
}

.rating-text {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #f39c12;
}

.section-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.detail-movie-overview {
    -fx-font-size: 16px;
    -fx-text-fill: #34495e;
    -fx-line-spacing: 4;
    -fx-font-weight: 400;
}

/* Genre Tags */
.detail-genre-container {
    -fx-max-width: 400;
}

.genre-tag {
    -fx-background-color: #ecf0f1;
    -fx-background-radius: 16;
    -fx-padding: 8 16;
    -fx-font-size: 14px;
    -fx-text-fill: #34495e;
    -fx-font-weight: 500;
}

.genre-tag.action {
    -fx-background-color: #ffe5e5;
    -fx-text-fill: #e74c3c;
}

.genre-tag.comedy {
    -fx-background-color: #fff3cd;
    -fx-text-fill: #f39c12;
}

.genre-tag.drama {
    -fx-background-color: #e8f4fd;
    -fx-text-fill: #3498db;
}

.genre-tag.thriller {
    -fx-background-color: #f0e6ff;
    -fx-text-fill: #9b59b6;
}

.genre-tag.horror {
    -fx-background-color: #ffe6e6;
    -fx-text-fill: #e74c3c;
}

.genre-tag.romance {
    -fx-background-color: #ffe6f0;
    -fx-text-fill: #e91e63;
}

.genre-tag.sci-fi {
    -fx-background-color: #e6f3ff;
    -fx-text-fill: #2196f3;
}

/* Detail Info */
.detail-info-label {
    -fx-font-size: 14px;
    -fx-text-fill: #7f8c8d;
    -fx-font-weight: 600;
    -fx-min-width: 100;
}

.detail-info-value {
    -fx-font-size: 14px;
    -fx-text-fill: #2c3e50;
    -fx-font-weight: 500;
}


