package oop2.project.model;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for the TmdbQuery class.
 */
public class TmdbQueryTest {

    @Test
    public void testDefaultConstructor() {
        TmdbQuery query = new TmdbQuery();
        
        assertEquals("", query.getText());
        assertNotNull(query.getLanguage());
        assertEquals(0, query.getPage());
        assertEquals(SortOption.getDefault(), query.getSortBy());
    }

    @Test
    public void testSetText() {
        TmdbQuery query = new TmdbQuery();
        query.setText("test movie");
        assertEquals("test movie", query.getText());
    }

    @Test
    public void testSetTextEmpty() {
        TmdbQuery query = new TmdbQuery();
        query.setText("");
        assertEquals("", query.getText());
    }

    @Test
    public void testSetLanguage() {
        TmdbQuery query = new TmdbQuery();
        query.setLanguage(Language.English);
        assertEquals(Language.English, query.getLanguage());
    }

    @Test
    public void testSetMinimumRating() {
        TmdbQuery query = new TmdbQuery();
        query.setMinimumRating(7.5);
        assertEquals(7.5, query.getMinimumRating());
    }

    @Test
    public void testIncludeAdultContent() {
        TmdbQuery query = new TmdbQuery();
        query.includeAdultContent(true);
        assertTrue(query.includeAdult());
        
        query.includeAdultContent(false);
        assertFalse(query.includeAdult());
    }

    @Test
    public void testSetPage() {
        TmdbQuery query = new TmdbQuery();
        query.setPage(5);
        assertEquals(5, query.getPage());
    }

    @Test
    public void testSetSortBy() {
        TmdbQuery query = new TmdbQuery();
        query.setSortBy(SortOption.TITLE_ASC);
        assertEquals(SortOption.TITLE_ASC, query.getSortBy());
    }

    @Test
    public void testFluentInterface() {
        TmdbQuery query = new TmdbQuery()
                .setText("test")
                .setLanguage(Language.English)
                .setMinimumRating(8.0)
                .includeAdultContent(false)
                .setPage(2)
                .setSortBy(SortOption.VOTE_AVERAGE_DESC);

        assertEquals("test", query.getText());
        assertEquals(Language.English, query.getLanguage());
        assertEquals(8.0, query.getMinimumRating());
        assertFalse(query.includeAdult());
        assertEquals(2, query.getPage());
        assertEquals(SortOption.VOTE_AVERAGE_DESC, query.getSortBy());
    }
}
