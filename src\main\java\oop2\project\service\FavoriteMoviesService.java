package oop2.project.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.tinylog.Logger;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Service for managing favorite/hearted movies.
 * Stores favorite movie IDs as JSO<PERSON> in the same directory as the application config.
 */
public class FavoriteMoviesService {
    
    private static final Path FAVORITES_FILE_PATH = Path.of(System.getProperty("user.home"), "oop2AppDiegoConfig", "favoriteMovies.json");
    
    private final ObjectMapper objectMapper;
    private final Set<Integer> favoriteMovieIds;
    private static final FavoriteMoviesService instance = new FavoriteMoviesService();
    
    private FavoriteMoviesService() {
        this.objectMapper = new ObjectMapper();
        this.favoriteMovieIds = ConcurrentHashMap.newKeySet();
        loadFavorites();
    }
    
    public static FavoriteMoviesService getInstance() {
        return instance;
    }
    
    /**
     * Loads favorite movie IDs from the JSON file.
     */
    private void loadFavorites() {
        try {
            if (Files.exists(FAVORITES_FILE_PATH)) {
                String json = Files.readString(FAVORITES_FILE_PATH);
                Set<Integer> loadedIds = objectMapper.readValue(json, new TypeReference<Set<Integer>>() {});
                favoriteMovieIds.clear();
                favoriteMovieIds.addAll(loadedIds);
                Logger.info("Loaded {} favorite movies", favoriteMovieIds.size());
            } else {
                Logger.info("No favorites file found, starting with empty favorites");
            }
        } catch (IOException e) {
            Logger.error(e, "Failed to load favorite movies from file");
        }
    }
    
    /**
     * Saves favorite movie IDs to the JSON file.
     */
    private void saveFavorites() {
        try {
            // Ensure the directory exists
            Files.createDirectories(FAVORITES_FILE_PATH.getParent());
            
            String json = objectMapper.writeValueAsString(favoriteMovieIds);
            Files.writeString(FAVORITES_FILE_PATH, json);
            Logger.debug("Saved {} favorite movies to file", favoriteMovieIds.size());
        } catch (IOException e) {
            Logger.error(e, "Failed to save favorite movies to file");
        }
    }
    
    /**
     * Adds a movie to favorites.
     * 
     * @param movieId the ID of the movie to add to favorites
     * @return true if the movie was added, false if it was already in favorites
     */
    public boolean addToFavorites(int movieId) {
        boolean added = favoriteMovieIds.add(movieId);
        if (added) {
            saveFavorites();
            Logger.info("Added movie {} to favorites", movieId);
        }
        return added;
    }
    
    /**
     * Removes a movie from favorites.
     * 
     * @param movieId the ID of the movie to remove from favorites
     * @return true if the movie was removed, false if it wasn't in favorites
     */
    public boolean removeFromFavorites(int movieId) {
        boolean removed = favoriteMovieIds.remove(movieId);
        if (removed) {
            saveFavorites();
            Logger.info("Removed movie {} from favorites", movieId);
        }
        return removed;
    }
    
    /**
     * Checks if a movie is in favorites.
     * 
     * @param movieId the ID of the movie to check
     * @return true if the movie is in favorites, false otherwise
     */
    public boolean isFavorite(int movieId) {
        return favoriteMovieIds.contains(movieId);
    }
    
    /**
     * Gets all favorite movie IDs.
     * 
     * @return a copy of the set of favorite movie IDs
     */
    public Set<Integer> getFavoriteMovieIds() {
        return new HashSet<>(favoriteMovieIds);
    }
    
    /**
     * Gets the number of favorite movies.
     * 
     * @return the number of favorite movies
     */
    public int getFavoriteCount() {
        return favoriteMovieIds.size();
    }
    
    /**
     * Toggles the favorite status of a movie.
     * 
     * @param movieId the ID of the movie to toggle
     * @return true if the movie is now in favorites, false if it was removed
     */
    public boolean toggleFavorite(int movieId) {
        if (isFavorite(movieId)) {
            removeFromFavorites(movieId);
            return false;
        } else {
            addToFavorites(movieId);
            return true;
        }
    }
    
    /**
     * Clears all favorites.
     */
    public void clearAllFavorites() {
        favoriteMovieIds.clear();
        saveFavorites();
        Logger.info("Cleared all favorite movies");
    }
}
