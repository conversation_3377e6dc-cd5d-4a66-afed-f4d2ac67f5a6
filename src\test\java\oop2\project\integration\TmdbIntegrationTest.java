package oop2.project.integration;

import oop2.project.model.MovieSearchResult;
import oop2.project.model.SortOption;
import oop2.project.model.TmdbQuery;
import oop2.project.service.TmdbService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for TMDB service functionality.
 * This test requires a valid TMDB API key to be configured.
 */
public class TmdbIntegrationTest {

    @Test
    @EnabledIf("hasApiKey")
    public void testDiscoverMovies() throws Exception {
        // Given
        TmdbService service = new TmdbService();
        TmdbQuery query = new TmdbQuery()
                .setMinimumRating(7.0)
                .includeAdultContent(false);

        // When
        CompletableFuture<MovieSearchResult> future = service.discoverMovies(query);
        MovieSearchResult result = future.get(10, TimeUnit.SECONDS);

        // Then
        assertNotNull(result, "Result should not be null");
        assertNotNull(result.getResults(), "Results list should not be null");
        assertTrue(result.getTotalResults() > 0, "Should have some results");
        assertTrue(result.getResults().size() > 0, "Should have movies in results");
        
        // Verify first movie has required fields
        if (!result.getResults().isEmpty()) {
            var firstMovie = result.getResults().get(0);
            assertNotNull(firstMovie.getTitle(), "Movie should have a title");
            assertTrue(firstMovie.getVoteAverage() >= 7.0, "Movie should meet minimum rating requirement");
        }
    }

    @Test
    @EnabledIf("hasApiKey")
    public void testDiscoverMoviesWithSorting() throws Exception {
        // Given
        TmdbService service = new TmdbService();
        TmdbQuery query = new TmdbQuery()
                .setMinimumRating(7.0)
                .includeAdultContent(false)
                .setSortBy(SortOption.VOTE_AVERAGE_DESC);

        // When
        CompletableFuture<MovieSearchResult> future = service.discoverMovies(query);
        MovieSearchResult result = future.get(10, TimeUnit.SECONDS);

        // Then
        assertNotNull(result, "Result should not be null");
        assertNotNull(result.getResults(), "Results list should not be null");
        assertTrue(result.getTotalResults() > 0, "Should have some results");

        // Verify movies are sorted by rating (descending)
        if (result.getResults().size() > 1) {
            var firstMovie = result.getResults().get(0);
            var secondMovie = result.getResults().get(1);
            assertTrue(firstMovie.getVoteAverage() >= secondMovie.getVoteAverage(),
                    "Movies should be sorted by vote average in descending order");
        }
    }

    @Test
    public void testQueryBuilding() {
        // Given
        TmdbQuery query = new TmdbQuery();

        // When
        query.setMinimumRating(8.5)
             .includeAdultContent(true)
             .setSortBy(SortOption.TITLE_ASC);

        // Then
        assertEquals(8.5, query.getMinimumRating(), 0.01);
        assertTrue(query.includeAdult());
        assertEquals(SortOption.TITLE_ASC, query.getSortBy());
    }

    /**
     * Helper method to check if API key is available for testing.
     * This prevents tests from failing when API key is not configured.
     */
    static boolean hasApiKey() {
        try {
            oop2.project.config.AppConfig config = oop2.project.config.AppConfig.getInstance();
            String apiKey = config.getTmdbApiKey();
            return apiKey != null && !apiKey.trim().isEmpty();
        } catch (Exception e) {
            return false;
        }
    }
}
