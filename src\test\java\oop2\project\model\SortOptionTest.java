package oop2.project.model;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for the SortOption enum.
 */
public class SortOptionTest {

    @Test
    public void testGetApiValue() {
        assertEquals("popularity.desc", SortOption.POPULARITY_DESC.getApiValue());
        assertEquals("popularity.asc", SortOption.POPULARITY_ASC.getApiValue());
        assertEquals("title.asc", SortOption.TITLE_ASC.getApiValue());
        assertEquals("title.desc", SortOption.TITLE_DESC.getApiValue());
        assertEquals("original_title.asc", SortOption.ORIGINAL_TITLE_ASC.getApiValue());
        assertEquals("original_title.desc", SortOption.ORIGINAL_TITLE_DESC.getApiValue());
        assertEquals("primary_release_date.desc", SortOption.RELEASE_DATE_DESC.getApiValue());
        assertEquals("primary_release_date.asc", SortOption.RELEASE_DATE_ASC.getApiValue());
        assertEquals("vote_average.desc", SortOption.VOTE_AVERAGE_DESC.getApiValue());
        assertEquals("vote_average.asc", SortOption.VOTE_AVERAGE_ASC.getApiValue());
        assertEquals("vote_count.desc", SortOption.VOTE_COUNT_DESC.getApiValue());
        assertEquals("vote_count.asc", SortOption.VOTE_COUNT_ASC.getApiValue());
        assertEquals("revenue.desc", SortOption.REVENUE_DESC.getApiValue());
        assertEquals("revenue.asc", SortOption.REVENUE_ASC.getApiValue());
    }

    @Test
    public void testGetDisplayName() {
        assertEquals("Popularity (High to Low)", SortOption.POPULARITY_DESC.getDisplayName());
        assertEquals("Popularity (Low to High)", SortOption.POPULARITY_ASC.getDisplayName());
        assertEquals("Title (A to Z)", SortOption.TITLE_ASC.getDisplayName());
        assertEquals("Title (Z to A)", SortOption.TITLE_DESC.getDisplayName());
        assertEquals("Original Title (A to Z)", SortOption.ORIGINAL_TITLE_ASC.getDisplayName());
        assertEquals("Original Title (Z to A)", SortOption.ORIGINAL_TITLE_DESC.getDisplayName());
        assertEquals("Release Date (Newest First)", SortOption.RELEASE_DATE_DESC.getDisplayName());
        assertEquals("Release Date (Oldest First)", SortOption.RELEASE_DATE_ASC.getDisplayName());
        assertEquals("Rating (High to Low)", SortOption.VOTE_AVERAGE_DESC.getDisplayName());
        assertEquals("Rating (Low to High)", SortOption.VOTE_AVERAGE_ASC.getDisplayName());
        assertEquals("Vote Count (High to Low)", SortOption.VOTE_COUNT_DESC.getDisplayName());
        assertEquals("Vote Count (Low to High)", SortOption.VOTE_COUNT_ASC.getDisplayName());
        assertEquals("Revenue (High to Low)", SortOption.REVENUE_DESC.getDisplayName());
        assertEquals("Revenue (Low to High)", SortOption.REVENUE_ASC.getDisplayName());
    }

    @Test
    public void testToString() {
        assertEquals("Popularity (High to Low)", SortOption.POPULARITY_DESC.toString());
        assertEquals("Title (A to Z)", SortOption.TITLE_ASC.toString());
        assertEquals("Rating (High to Low)", SortOption.VOTE_AVERAGE_DESC.toString());
    }

    @Test
    public void testGetDefault() {
        assertEquals(SortOption.POPULARITY_DESC, SortOption.getDefault());
    }

    @Test
    public void testAllSortOptionsHaveValidApiValues() {
        for (SortOption option : SortOption.values()) {
            assertNotNull(option.getApiValue());
            assertFalse(option.getApiValue().isEmpty());
            assertNotNull(option.getDisplayName());
            assertFalse(option.getDisplayName().isEmpty());
        }
    }

    @Test
    public void testSortOptionCount() {
        // Ensure we have all 14 expected sort options
        assertEquals(14, SortOption.values().length);
    }
}
