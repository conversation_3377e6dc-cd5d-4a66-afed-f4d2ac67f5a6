<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<?scenebuilder-stylesheet ../css/MovieCard.css?>

<VBox xmlns="http://javafx.com/javafx/17.0.12"
      xmlns:fx="http://javafx.com/fxml/1"
      fx:id="movieCard"
      fx:controller="oop2.project.controller.MovieCardController"
      styleClass="movie-card"
      prefWidth="280.0"
      maxWidth="280.0"
      spacing="0.0">

    <!-- Movie Poster Container -->
    <StackPane styleClass="poster-container">
        <!-- Movie Poster -->
        <ImageView fx:id="moviePosterImage" 
                  fitWidth="280.0" 
                  fitHeight="420.0" 
                  preserveRatio="true"
                  styleClass="movie-poster"/>
        
        <!-- Placeholder for missing poster -->
        <VBox fx:id="posterPlaceholder" 
              alignment="CENTER" 
              spacing="10.0"
              styleClass="poster-placeholder"
              visible="false">
            <FontIcon iconLiteral="fas-film" iconSize="48" styleClass="placeholder-icon"/>
            <Label text="No Image" styleClass="placeholder-text"/>
        </VBox>
        

        
        <!-- Favorite Button -->
        <Button fx:id="favoriteButton"
                styleClass="favorite-btn"
                StackPane.alignment="TOP_LEFT">
            <StackPane.margin>
                <Insets top="12.0" left="12.0"/>
            </StackPane.margin>
            <graphic>
                <FontIcon fx:id="favoriteIcon" iconLiteral="far-heart" iconSize="16"/>
            </graphic>
        </Button>
        

    </StackPane>

    <!-- Movie Info Section -->
    <VBox spacing="12.0" styleClass="movie-info">
        <VBox.margin>
            <Insets top="15.0" bottom="15.0" left="15.0" right="15.0"/>
        </VBox.margin>
        
        <!-- Title and Year -->
        <VBox spacing="4.0">
            <Label fx:id="movieTitleLabel"
                   text="The Dark Knight"
                   styleClass="movie-title"
                   wrapText="true"/>
            <HBox spacing="12.0" alignment="CENTER_LEFT">
                <Label fx:id="movieYearLabel" text="2008" styleClass="movie-year"/>
                <!-- Rating Badge -->
                <HBox fx:id="ratingBadge"
                      alignment="CENTER"
                      spacing="4.0"
                      styleClass="rating-badge">
                    <FontIcon iconLiteral="fas-star" iconSize="12" styleClass="rating-star"/>
                    <Label fx:id="ratingLabel" text="8.5" styleClass="rating-text"/>
                </HBox>
            </HBox>
        </VBox>

    </VBox>
</VBox>
