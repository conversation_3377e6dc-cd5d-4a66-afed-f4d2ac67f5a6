<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<?scenebuilder-stylesheet ../css/MovieDetailView.css?>

<BorderPane xmlns="http://javafx.com/javafx/17.0.12"
           xmlns:fx="http://javafx.com/fxml/1"
           fx:controller="oop2.project.controller.MovieDetailViewController"
           styleClass="movie-detail-view"
           prefWidth="800.0"
           prefHeight="600.0">



    <!-- Main content -->
    <center>
        <ScrollPane fitToWidth="true" styleClass="content-scroll">
            <content>
                <VBox spacing="30.0" styleClass="content-container">
                    <padding>
                        <Insets top="20.0" bottom="30.0" left="30.0" right="30.0"/>
                    </padding>

                    <!-- Movie header with poster and basic info -->
                    <HBox spacing="30.0" alignment="TOP_LEFT">
                        <!-- Movie Poster -->
                        <StackPane styleClass="detail-poster-container">
                            <ImageView fx:id="moviePosterImage" 
                                      fitWidth="300.0" 
                                      fitHeight="450.0" 
                                      preserveRatio="true"
                                      styleClass="detail-movie-poster"/>
                            
                            <VBox fx:id="posterPlaceholder" 
                                  alignment="CENTER" 
                                  spacing="15.0"
                                  styleClass="detail-poster-placeholder"
                                  visible="false">
                                <FontIcon iconLiteral="fas-film" iconSize="64" styleClass="placeholder-icon"/>
                                <Label text="No Image Available" styleClass="placeholder-text"/>
                            </VBox>
                        </StackPane>

                        <!-- Movie Information -->
                        <VBox spacing="20.0" HBox.hgrow="ALWAYS">
                            <!-- Title and Year -->
                            <VBox spacing="8.0">
                                <Label fx:id="movieTitleLabel" 
                                       text="The Dark Knight" 
                                       styleClass="detail-movie-title"
                                       wrapText="true"/>
                                <HBox spacing="15.0" alignment="CENTER_LEFT">
                                    <Label fx:id="movieYearLabel" text="2008" styleClass="detail-movie-year"/>
                                    <Label fx:id="movieDurationLabel" text="152 min" styleClass="detail-movie-duration"/>
                                    <HBox spacing="4.0" alignment="CENTER_LEFT" styleClass="rating-container">
                                        <FontIcon iconLiteral="fas-star" iconSize="14" styleClass="rating-star"/>
                                        <Label fx:id="ratingLabel" text="8.5" styleClass="rating-text"/>
                                    </HBox>
                                </HBox>
                            </VBox>

                            <!-- Genres -->
                            <VBox spacing="8.0">
                                <Label text="Genres" styleClass="section-title"/>
                                <FlowPane fx:id="genreContainer" 
                                         hgap="8.0" 
                                         vgap="8.0"
                                         styleClass="detail-genre-container">
                                    <!-- Genre tags will be added dynamically -->
                                </FlowPane>
                            </VBox>

                            <!-- Overview -->
                            <VBox spacing="8.0">
                                <Label text="Overview" styleClass="section-title"/>
                                <Label fx:id="movieOverviewLabel" 
                                       text="When the menace known as the Joker wreaks havoc and chaos on the people of Gotham, Batman must accept one of the greatest psychological and physical tests of his ability to fight injustice."
                                       styleClass="detail-movie-overview"
                                       wrapText="true"/>
                            </VBox>

                            <!-- Additional Details -->
                            <VBox spacing="12.0">
                                <Label text="Details" styleClass="section-title"/>
                                <VBox spacing="8.0">
                                    <HBox spacing="10.0" alignment="CENTER_LEFT">
                                        <Label text="Release Date:" styleClass="detail-info-label"/>
                                        <Label fx:id="releaseDateLabel" text="July 18, 2008" styleClass="detail-info-value"/>
                                    </HBox>
                                    <HBox spacing="10.0" alignment="CENTER_LEFT">
                                        <Label text="Vote Count:" styleClass="detail-info-label"/>
                                        <Label fx:id="voteCountLabel" text="25,847" styleClass="detail-info-value"/>
                                    </HBox>
                                    <HBox spacing="10.0" alignment="CENTER_LEFT">
                                        <Label text="Popularity:" styleClass="detail-info-label"/>
                                        <Label fx:id="popularityLabel" text="98.5" styleClass="detail-info-value"/>
                                    </HBox>
                                </VBox>
                            </VBox>


                        </VBox>
                    </HBox>
                </VBox>
            </content>
        </ScrollPane>
    </center>
</BorderPane>
