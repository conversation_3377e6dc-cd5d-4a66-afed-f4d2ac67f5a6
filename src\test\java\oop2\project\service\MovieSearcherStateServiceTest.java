package oop2.project.service;

import oop2.project.model.Language;
import oop2.project.model.MovieSearcherState;
import oop2.project.model.SortOption;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for MovieSearcherStateService.
 */
public class MovieSearcherStateServiceTest {

    private MovieSearcherStateService stateService;

    @BeforeEach
    public void setUp() {
        stateService = MovieSearcherStateService.getInstance();
        // Clear any existing state before each test
        stateService.clearState();
    }

    @Test
    public void testSaveAndRestoreState() {
        // Given - create a state with specific values
        MovieSearcherState originalState = new MovieSearcherState();
        originalState.setSearchText("Test Movie");
        originalState.setSelectedLanguage(Language.English);
        originalState.setMinimumRating(7.5);
        originalState.setIncludeAdultContent(true);
        originalState.setSelectedSortOption(SortOption.VOTE_AVERAGE_DESC);
        originalState.setCurrentPage(3);
        originalState.setHasPerformedSearch(true);
        originalState.setCurrentViewState(MovieSearcherState.ViewState.CONTENT);

        // When - save the state
        stateService.saveState(originalState);

        // Then - restore should return the same values
        MovieSearcherState restoredState = stateService.restoreState();
        
        assertEquals("Test Movie", restoredState.getSearchText());
        assertEquals(Language.English, restoredState.getSelectedLanguage());
        assertEquals(7.5, restoredState.getMinimumRating(), 0.01);
        assertTrue(restoredState.isIncludeAdultContent());
        assertEquals(SortOption.VOTE_AVERAGE_DESC, restoredState.getSelectedSortOption());
        assertEquals(3, restoredState.getCurrentPage());
        assertTrue(restoredState.hasPerformedSearch());
        assertEquals(MovieSearcherState.ViewState.CONTENT, restoredState.getCurrentViewState());
    }

    @Test
    public void testHasSavedState() {
        // Given - no state saved initially
        assertFalse(stateService.hasSavedState());

        // When - save a state with performed search
        MovieSearcherState state = new MovieSearcherState();
        state.setHasPerformedSearch(true);
        stateService.saveState(state);

        // Then - should have saved state
        assertTrue(stateService.hasSavedState());
    }

    @Test
    public void testHasActiveSearchSession() {
        // Given - no state saved initially
        assertFalse(stateService.hasActiveSearchSession());

        // When - save a state with search text but no performed search
        MovieSearcherState state = new MovieSearcherState();
        state.setSearchText("Test");
        state.setHasPerformedSearch(false);
        stateService.saveState(state);

        // Then - should not have active session
        assertFalse(stateService.hasActiveSearchSession());

        // When - mark as performed search
        state.setHasPerformedSearch(true);
        stateService.saveState(state);

        // Then - should have active session
        assertTrue(stateService.hasActiveSearchSession());
    }

    @Test
    public void testClearState() {
        // Given - save some state
        MovieSearcherState state = new MovieSearcherState();
        state.setSearchText("Test");
        state.setHasPerformedSearch(true);
        stateService.saveState(state);
        assertTrue(stateService.hasSavedState());

        // When - clear state
        stateService.clearState();

        // Then - should not have saved state
        assertFalse(stateService.hasSavedState());
    }

    @Test
    public void testResetSearchResults() {
        // Given - save state with search results
        MovieSearcherState state = new MovieSearcherState();
        state.setSearchText("Test Movie");
        state.setSelectedLanguage(Language.English);
        state.setCurrentPage(3);
        state.setHasPerformedSearch(true);
        stateService.saveState(state);

        // When - reset search results
        stateService.resetSearchResults();

        // Then - search data should be cleared but filters preserved
        MovieSearcherState restoredState = stateService.restoreState();
        assertEquals("", restoredState.getSearchText());
        assertEquals(1, restoredState.getCurrentPage());
        assertFalse(restoredState.hasPerformedSearch());
        assertEquals(MovieSearcherState.ViewState.INITIAL, restoredState.getCurrentViewState());
        
        // But language filter should be preserved
        assertEquals(Language.English, restoredState.getSelectedLanguage());
    }

    @Test
    public void testSingletonBehavior() {
        // Given - get two instances
        MovieSearcherStateService instance1 = MovieSearcherStateService.getInstance();
        MovieSearcherStateService instance2 = MovieSearcherStateService.getInstance();

        // Then - should be the same instance
        assertSame(instance1, instance2);
    }
}
