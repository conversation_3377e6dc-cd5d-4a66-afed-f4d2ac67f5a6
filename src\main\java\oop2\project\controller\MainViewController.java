package oop2.project.controller;

import javafx.fxml.*;
import javafx.scene.*;
import javafx.scene.control.Button;
import javafx.scene.control.Tooltip;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.*;
import oop2.project.Main;
import oop2.project.config.AppConfig;
import org.tinylog.Logger;

import java.io.IOException;

public class MainViewController {

    @FXML
    private StackPane contentArea;

    @FXML
    private Button movieSearcherNavButton;

    @FXML
    private Button heartedMoviesNavButton;

    @FXML
    private Button settingsNavButton;

    private final AppConfig appConfig = AppConfig.getInstance();

    // Cache controllers to maintain state
    private MovieSearcherController movieSearcherController;
    private Parent movieSearcherView;
    private Parent heartedMoviesView;
    private Parent settingsView;

    // Track current view
    private ViewType currentView = ViewType.NONE;

    private enum ViewType {
        NONE, MOVIE_SEARCHER, HEARTED_MOVIES, SETTINGS
    }

    @FXML
    public void initialize() {
        // Update button state based on config validity
        updateButtonStates();

        if(appConfig.isValid()) {
            showMovieSearcher();
        } else {
            showSettingsView();
        }
    }

    @FXML
    private void onMovieSearcherButtonClicked() {
        // Only allow navigation if config is valid
        if (appConfig.isValid()) {
            showMovieSearcher();
        }
    }

    @FXML
    private void onHeartedMoviesButtonClicked() {
        // Only allow navigation if config is valid
        if (appConfig.isValid()) {
            showHeartedMovies();
        }
    }

    private void showMovieSearcher() {
        try {
            // Save current view state if switching from movie searcher
            saveCurrentViewState();

            // Load movie searcher view if not already cached
            if (movieSearcherView == null) {
                FXMLLoader loader = new FXMLLoader(Main.class.getResource("view/MovieSearcher.fxml"));
                movieSearcherView = loader.load();
                movieSearcherController = loader.getController();
            }

            contentArea.getChildren().clear();
            contentArea.getChildren().add(movieSearcherView);
            currentView = ViewType.MOVIE_SEARCHER;
        } catch (IOException e) {
            System.out.println("Error loading movie searcher view " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void showHeartedMovies() {
        try {
            // Save current view state if switching from movie searcher
            saveCurrentViewState();

            // Load hearted movies view if not already cached
            if (heartedMoviesView == null) {
                FXMLLoader loader = new FXMLLoader(Main.class.getResource("view/HeartedMovies.fxml"));
                heartedMoviesView = loader.load();
            }

            contentArea.getChildren().clear();
            contentArea.getChildren().add(heartedMoviesView);
            currentView = ViewType.HEARTED_MOVIES;
        } catch (IOException e) {
            System.out.println("Error loading hearted movies view " + e.getMessage());
            e.printStackTrace();
        }
    }

    @FXML
    private void onSettingsButtonClicked() {
        showSettingsView();
    }

    private void showSettingsView() {
        try {
            // Save current view state if switching from movie searcher
            saveCurrentViewState();

            // Load settings view if not already cached
            if (settingsView == null) {
                FXMLLoader loader = new FXMLLoader(Main.class.getResource("view/SettingsView.fxml"));
                settingsView = loader.load();

                // Get the settings controller to set up communication
                SettingsViewController settingsController = loader.getController();
                settingsController.setMainViewController(this);
            }

            contentArea.getChildren().clear();
            contentArea.getChildren().add(settingsView);
            currentView = ViewType.SETTINGS;
        } catch (IOException e) {
            // TODO: Logging
            System.out.println( "Error loading settings view" + e.getMessage());
        }
    }

    /**
     * Called by SettingsViewController when settings are updated
     */
    public void onSettingsUpdated() {
        updateButtonStates();
    }

    private void updateButtonStates() {
        boolean isValid = appConfig.isValid();

        // Update movie searcher button
        movieSearcherNavButton.setDisable(!isValid);
        heartedMoviesNavButton.setDisable(!isValid);

        if (isValid) {
            movieSearcherNavButton.setTooltip(null);
            heartedMoviesNavButton.setTooltip(null);
        } else {
            Tooltip tooltip = new Tooltip("API keys are required. Please configure them in Settings.");
            tooltip.setShowDelay(javafx.util.Duration.millis(300));
            movieSearcherNavButton.setTooltip(tooltip);

            Tooltip heartedTooltip = new Tooltip("API keys are required. Please configure them in Settings.");
            heartedTooltip.setShowDelay(javafx.util.Duration.millis(300));
            heartedMoviesNavButton.setTooltip(heartedTooltip);
        }
    }

    @FXML
    private void onExitButtonClicked(MouseEvent mouseEvent) {
        System.exit(0);
    }

    /**
     * Saves the current view state before switching to another view.
     * This ensures that the movie searcher state is preserved when navigating away.
     */
    private void saveCurrentViewState() {
        if (currentView == ViewType.MOVIE_SEARCHER && movieSearcherController != null) {
            movieSearcherController.saveCurrentState();
            Logger.debug("Saved movie searcher state before view switch");
        }
    }
}
