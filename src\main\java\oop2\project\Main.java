package oop2.project;

import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.*;
import javafx.stage.*;

import org.kordamp.bootstrapfx.BootstrapFX;
import org.tinylog.Logger;

import java.io.IOException;

/**
 * Main application class for the TMDB Movie Explorer.
 */
public class Main extends Application {

    //private static HostServices hostServicesInstance;

    @Override
    public void start(Stage stage) throws IOException {
        Logger.info("Starting TMDB Movie Explorer application");

        // Store host services for URL opening
        //hostServicesInstance = super.getHostServices();

        try {
            // Set application title and icon
            stage.setTitle("TMDB Movie Explorer");

            // Load the appropriate view
            FXMLLoader fxmlLoader = new FXMLLoader(Main.class.getResource("view/MainView.fxml"));

            // Create scene
            Scene scene = new Scene(fxmlLoader.load(), 1400, 800);

            // Add BootstrapFX stylesheet for better styling
            scene.getStylesheets().add(BootstrapFX.bootstrapFXStylesheet());

            // Set the scene and show the stage
            stage.setScene(scene);
            stage.show();

            Logger.info("Application started successfully");
        } catch (Exception e) {
            Logger.error(e, "Error starting application");
            throw e;
        }
    }
    /**
     * Main method to launch the application.
     *
     * @param args command line arguments
     */
    public static void main(String[] args) {
        launch();
    }
}
