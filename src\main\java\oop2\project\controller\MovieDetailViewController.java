package oop2.project.controller;

import javafx.fxml.FXML;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.FlowPane;
import javafx.scene.layout.VBox;
import oop2.project.model.Movie;

import org.tinylog.Logger;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Optional;

/**
 * Controller for the movie detail view window.
 * Displays comprehensive information about a selected movie.
 */
public class MovieDetailViewController {


    
    @FXML
    private ImageView moviePosterImage;
    
    @FXML
    private VBox posterPlaceholder;
    
    @FXML
    private Label movieTitleLabel;
    
    @FXML
    private Label movieYearLabel;
    
    @FXML
    private Label movieDurationLabel;
    
    @FXML
    private Label ratingLabel;
    
    @FXML
    private FlowPane genreContainer;
    
    @FXML
    private Label movieOverviewLabel;
    
    @FXML
    private Label releaseDateLabel;
    
    @FXML
    private Label voteCountLabel;
    
    @FXML
    private Label popularityLabel;

    private Movie movie;

    @FXML
    public void initialize() {
        setupEventHandlers();
    }

    /**
     * Sets up event handlers for UI interactions.
     */
    private void setupEventHandlers() {
        // No event handlers needed since close button was removed
    }

    /**
     * Sets the movie to display and updates all UI elements.
     * 
     * @param movie the movie to display
     */
    public void setMovie(Movie movie) {
        if (movie == null) {
            Logger.warn("Attempted to set null movie in MovieDetailViewController");
            return;
        }
        
        this.movie = movie;
        updateMovieDisplay();
    }

    /**
     * Updates all movie display elements.
     */
    private void updateMovieDisplay() {
        updatePoster();
        updateBasicInfo();
        updateGenres();
        updateOverview();
        updateAdditionalDetails();
    }

    /**
     * Updates the movie poster image or shows placeholder.
     */
    private void updatePoster() {
        String posterPath = movie.getPosterPath();
        String posterUrl = null;

        if (posterPath != null && !posterPath.isEmpty()) {
            posterUrl = "https://image.tmdb.org/t/p/w500" + posterPath;
        }

        if (posterUrl != null && !posterUrl.isEmpty()) {
            try {
                Image posterImage = new Image(posterUrl, true); // Load in background
                moviePosterImage.setImage(posterImage);
                moviePosterImage.setVisible(true);
                posterPlaceholder.setVisible(false);
            } catch (Exception e) {
                Logger.debug("Failed to load poster image for movie: {}", movie.getTitle());
                showPosterPlaceholder();
            }
        } else {
            showPosterPlaceholder();
        }
    }

    /**
     * Shows the poster placeholder when image is not available.
     */
    private void showPosterPlaceholder() {
        moviePosterImage.setVisible(false);
        posterPlaceholder.setVisible(true);
    }

    /**
     * Updates basic movie information (title, year, rating, duration).
     */
    private void updateBasicInfo() {
        movieTitleLabel.setText(Optional.ofNullable(movie.getTitle()).orElse("Unknown Title"));

        Integer year = extractReleaseYear();
        movieYearLabel.setText(year != null ? year.toString() : "Unknown");

        ratingLabel.setText(String.format("%.1f", movie.getVoteAverage()));

        // Duration might not be available for all movies
        if (movie.getRuntime() > 0) {
            movieDurationLabel.setText(movie.getRuntime() + " min");
            movieDurationLabel.setVisible(true);
        } else {
            movieDurationLabel.setVisible(false);
        }
    }

    /**
     * Extracts the release year from the release date string.
     * 
     * @return the release year or null if not available
     */
    private Integer extractReleaseYear() {
        String releaseDate = movie.getReleaseDate();
        if (releaseDate != null && !releaseDate.isEmpty()) {
            try {
                return LocalDate.parse(releaseDate).getYear();
            } catch (DateTimeParseException e) {
                Logger.debug("Failed to parse release date: {}", releaseDate);
                return null;
            }
        }
        return null;
    }

    /**
     * Updates the genre display.
     */
    private void updateGenres() {
        genreContainer.getChildren().clear();

        List<Integer> genreIds = movie.getGenreIds();
        if (genreIds != null && !genreIds.isEmpty()) {
            genreIds.stream()
                    .map(this::createGenreLabel)
                    .forEach(genreContainer.getChildren()::add);
        }
    }

    /**
     * Creates a genre label for the given genre ID.
     * 
     * @param genreId the genre ID
     * @return a Label with appropriate styling
     */
    private Label createGenreLabel(Integer genreId) {
        Label genreLabel = new Label(getGenreName(genreId));
        genreLabel.getStyleClass().addAll("genre-tag", getGenreStyleClass(genreId));
        return genreLabel;
    }

    /**
     * Updates the movie overview.
     */
    private void updateOverview() {
        String overview = movie.getOverview();
        if (overview != null && !overview.isEmpty()) {
            movieOverviewLabel.setText(overview);
            movieOverviewLabel.setVisible(true);
        } else {
            movieOverviewLabel.setText("No overview available.");
            movieOverviewLabel.setVisible(true);
        }
    }

    /**
     * Updates additional movie details.
     */
    private void updateAdditionalDetails() {
        // Release date
        String releaseDate = movie.getReleaseDate();
        if (releaseDate != null && !releaseDate.isEmpty()) {
            try {
                LocalDate date = LocalDate.parse(releaseDate);
                releaseDateLabel.setText(date.format(DateTimeFormatter.ofPattern("MMMM d, yyyy")));
            } catch (DateTimeParseException e) {
                releaseDateLabel.setText(releaseDate);
            }
        } else {
            releaseDateLabel.setText("Unknown");
        }

        // Vote count
        voteCountLabel.setText(String.format("%,d", movie.getVoteCount()));

        // Popularity
        popularityLabel.setText(String.format("%.1f", movie.getPopularity()));
    }

    /**
     * Maps genre ID to human-readable name.
     * 
     * @param genreId the genre ID
     * @return the genre name
     */
    private String getGenreName(Integer genreId) {
        return switch (genreId) {
            case 28 -> "Action";
            case 12 -> "Adventure";
            case 16 -> "Animation";
            case 35 -> "Comedy";
            case 80 -> "Crime";
            case 99 -> "Documentary";
            case 18 -> "Drama";
            case 10751 -> "Family";
            case 14 -> "Fantasy";
            case 36 -> "History";
            case 27 -> "Horror";
            case 10402 -> "Music";
            case 9648 -> "Mystery";
            case 10749 -> "Romance";
            case 878 -> "Sci-Fi";
            case 10770 -> "TV Movie";
            case 53 -> "Thriller";
            case 10752 -> "War";
            case 37 -> "Western";
            default -> "Other";
        };
    }

    /**
     * Maps genre ID to CSS style class for color coding.
     * 
     * @param genreId the genre ID
     * @return the CSS style class
     */
    private String getGenreStyleClass(Integer genreId) {
        return switch (genreId) {
            case 28 -> "action";
            case 35 -> "comedy";
            case 18 -> "drama";
            case 53 -> "thriller";
            case 27 -> "horror";
            case 10749 -> "romance";
            case 878 -> "sci-fi";
            default -> "";
        };
    }




}
