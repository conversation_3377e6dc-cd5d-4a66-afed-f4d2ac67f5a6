package oop2.project.model;

/**
 * Represents the complete state of the movie searcher view.
 * This class encapsulates all user inputs and search results that should be preserved
 * when navigating away from and back to the movie searcher.
 */
public class MovieSearcherState {
    
    // Search and filter state
    private String searchText = "";
    private Language selectedLanguage;
    private double minimumRating = 0.0;
    private boolean includeAdultContent = false;
    private SortOption selectedSortOption;
    
    // Pagination and results state
    private int currentPage = 1;
    private MovieSearchResult lastSearchResult;
    
    // UI state
    private boolean hasPerformedSearch = false;
    private ViewState currentViewState = ViewState.INITIAL;
    
    /**
     * Enum representing the current state of the view
     */
    public enum ViewState {
        INITIAL,    // Initial state, no search performed yet
        LOADING,    // Search in progress
        CONTENT,    // Results displayed
        EMPTY,      // No results found
        ERROR       // Error occurred
    }
    
    /**
     * Default constructor initializing with default values
     */
    public MovieSearcherState() {
        // Initialize with default values - will be set from AppConfig when used
    }
    
    /**
     * Copy constructor for creating a deep copy of the state
     */
    public MovieSearcherState(MovieSearcherState other) {
        this.searchText = other.searchText;
        this.selectedLanguage = other.selectedLanguage;
        this.minimumRating = other.minimumRating;
        this.includeAdultContent = other.includeAdultContent;
        this.selectedSortOption = other.selectedSortOption;
        this.currentPage = other.currentPage;
        this.lastSearchResult = other.lastSearchResult;
        this.hasPerformedSearch = other.hasPerformedSearch;
        this.currentViewState = other.currentViewState;
    }
    
    // Getters and setters
    
    public String getSearchText() {
        return searchText;
    }
    
    public void setSearchText(String searchText) {
        this.searchText = searchText != null ? searchText : "";
    }
    
    public Language getSelectedLanguage() {
        return selectedLanguage;
    }
    
    public void setSelectedLanguage(Language selectedLanguage) {
        this.selectedLanguage = selectedLanguage;
    }
    
    public double getMinimumRating() {
        return minimumRating;
    }
    
    public void setMinimumRating(double minimumRating) {
        this.minimumRating = minimumRating;
    }
    
    public boolean isIncludeAdultContent() {
        return includeAdultContent;
    }
    
    public void setIncludeAdultContent(boolean includeAdultContent) {
        this.includeAdultContent = includeAdultContent;
    }
    
    public SortOption getSelectedSortOption() {
        return selectedSortOption;
    }
    
    public void setSelectedSortOption(SortOption selectedSortOption) {
        this.selectedSortOption = selectedSortOption;
    }
    
    public int getCurrentPage() {
        return currentPage;
    }
    
    public void setCurrentPage(int currentPage) {
        this.currentPage = Math.max(1, currentPage);
    }
    
    public MovieSearchResult getLastSearchResult() {
        return lastSearchResult;
    }
    
    public void setLastSearchResult(MovieSearchResult lastSearchResult) {
        this.lastSearchResult = lastSearchResult;
    }
    
    public boolean hasPerformedSearch() {
        return hasPerformedSearch;
    }
    
    public void setHasPerformedSearch(boolean hasPerformedSearch) {
        this.hasPerformedSearch = hasPerformedSearch;
    }
    
    public ViewState getCurrentViewState() {
        return currentViewState;
    }
    
    public void setCurrentViewState(ViewState currentViewState) {
        this.currentViewState = currentViewState != null ? currentViewState : ViewState.INITIAL;
    }
    
    /**
     * Resets the state to initial values while preserving filter preferences
     */
    public void resetToInitial() {
        this.searchText = "";
        this.currentPage = 1;
        this.lastSearchResult = null;
        this.hasPerformedSearch = false;
        this.currentViewState = ViewState.INITIAL;
        // Note: We preserve filter settings (language, rating, adult content, sort)
    }
    
    /**
     * Creates a TmdbQuery from the current state
     */
    public TmdbQuery toTmdbQuery() {
        TmdbQuery query = new TmdbQuery();
        
        if (selectedLanguage != null) {
            query.setLanguage(selectedLanguage);
        }
        
        if (minimumRating > 0) {
            query.setMinimumRating(minimumRating);
        }
        
        query.includeAdultContent(includeAdultContent)
             .setText(searchText)
             .setPage(currentPage);
        
        if (selectedSortOption != null) {
            query.setSortBy(selectedSortOption);
        }
        
        return query;
    }
    
    @Override
    public String toString() {
        return String.format("MovieSearcherState{searchText='%s', page=%d, language=%s, rating=%.1f, adult=%s, sort=%s, viewState=%s}",
                searchText, currentPage, selectedLanguage, minimumRating, includeAdultContent, selectedSortOption, currentViewState);
    }
}
