package oop2.project.controller;

import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.VBox;
import javafx.stage.Modality;
import javafx.stage.Stage;
import oop2.project.model.Movie;
import oop2.project.Main;
import oop2.project.service.FavoriteMoviesService;
import org.kordamp.ikonli.javafx.FontIcon;
import org.tinylog.Logger;

import java.io.IOException;
import java.util.Optional;

/**
 * Controller for individual movie card components.
 * Handles the presentation logic for displaying movie information in card format.
 */
public class MovieCardController {

    @FXML
    private ImageView moviePosterImage;
    
    @FXML
    private VBox posterPlaceholder;
    
    @FXML
    private Label ratingLabel;
    
    @FXML
    private Label movieTitleLabel;
    
    @FXML
    private Label movieYearLabel;

    @FXML
    private Button favoriteButton;

    @FXML
    private VBox movieCard;

    private Movie movie;
    private final FavoriteMoviesService favoriteMoviesService;
    private Runnable onFavoriteChangedCallback;

    /**
     * Constructor to initialize the favorite movies service.
     */
    public MovieCardController() {
        this.favoriteMoviesService = FavoriteMoviesService.getInstance();
    }

    /**
     * Initializes the movie card with the provided movie data.
     *
     * @param movie the movie to display
     */
    public void setMovie(Movie movie) {
        if (movie == null) {
            Logger.warn("Attempted to set null movie in MovieCardController");
            return;
        }
        
        this.movie = movie;
        updateMovieDisplay();
    }

    /**
     * Updates all UI elements with the current movie data.
     */
    private void updateMovieDisplay() {
        updatePoster();
        updateBasicInfo();
        setupClickHandler();
        setupHeartButton();
    }

    /**
     * Updates the movie poster image or shows placeholder.
     */
    private void updatePoster() {
        String posterPath = movie.getPosterPath();
        String posterUrl = null;

        if (posterPath != null && !posterPath.isEmpty()) {
            posterUrl = "https://image.tmdb.org/t/p/w500" + posterPath;
        }

        if (posterUrl != null && !posterUrl.isEmpty()) {
            try {
                Image posterImage = new Image(posterUrl, true); // Load in background
                moviePosterImage.setImage(posterImage);
                moviePosterImage.setVisible(true);
                posterPlaceholder.setVisible(false);
            } catch (Exception e) {
                Logger.debug("Failed to load poster image for movie: {}", movie.getTitle());
                showPosterPlaceholder();
            }
        } else {
            showPosterPlaceholder();
        }
    }

    /**
     * Shows the poster placeholder when image is not available.
     */
    private void showPosterPlaceholder() {
        moviePosterImage.setVisible(false);
        posterPlaceholder.setVisible(true);
    }

    /**
     * Updates basic movie information (title, year, rating, duration).
     */
    private void updateBasicInfo() {
        movieTitleLabel.setText(Optional.ofNullable(movie.getTitle()).orElse("Unknown Title"));

        Integer year = extractReleaseYear();
        movieYearLabel.setText(year != null ? year.toString() : "Unknown");

        ratingLabel.setText(String.format("%.1f", movie.getVoteAverage()));
    }

    /**
     * Extracts the release year from the release date string.
     *
     * @return the release year or null if not available
     */
    private Integer extractReleaseYear() {
        String releaseDate = movie.getReleaseDate();
        if (releaseDate == null || releaseDate.isEmpty()) {
            return null;
        }

        try {
            // Release date format is typically "YYYY-MM-DD"
            return Integer.parseInt(releaseDate.substring(0, 4));
        } catch (Exception e) {
            Logger.debug("Failed to parse release year from: {}", releaseDate);
            return null;
        }
    }

    /**
     * Sets up click handler for the movie card to open detail view.
     */
    private void setupClickHandler() {
        if (movieCard != null) {
            movieCard.setOnMouseClicked(this::onCardClicked);
        }
    }

    /**
     * Sets up the heart button functionality and updates its appearance.
     */
    private void setupHeartButton() {
        if (favoriteButton != null && movie != null) {
            // Set up click handler
            favoriteButton.setOnAction(event -> onHeartButtonClicked());

            // Update heart button appearance based on favorite status
            updateHeartButtonAppearance();
        }
    }

    /**
     * Updates the heart button appearance based on whether the movie is favorited.
     */
    private void updateHeartButtonAppearance() {
        if (favoriteButton != null && movie != null) {
            boolean isFavorite = favoriteMoviesService.isFavorite(movie.getId());

            // Get the FontIcon from the button's graphic
            if (favoriteButton.getGraphic() instanceof FontIcon heartIcon) {
                if (isFavorite) {
                    heartIcon.setIconLiteral("fas-heart");
                    if (!favoriteButton.getStyleClass().contains("favorited")) {
                        favoriteButton.getStyleClass().add("favorited");
                    }
                } else {
                    heartIcon.setIconLiteral("far-heart");
                    favoriteButton.getStyleClass().remove("favorited");
                }
            }
        }
    }

    /**
     * Handles heart button click to toggle favorite status.
     */
    private void onHeartButtonClicked() {
        if (movie != null) {
            boolean isNowFavorite = favoriteMoviesService.toggleFavorite(movie.getId());
            updateHeartButtonAppearance();

            Logger.info("Movie '{}' {} favorites", movie.getTitle(),
                       isNowFavorite ? "added to" : "removed from");

            // Notify callback if set (for refreshing views)
            if (onFavoriteChangedCallback != null) {
                onFavoriteChangedCallback.run();
            }
        }
    }

    /**
     * Sets a callback to be executed when the favorite status changes.
     *
     * @param callback the callback to execute
     */
    public void setOnFavoriteChangedCallback(Runnable callback) {
        this.onFavoriteChangedCallback = callback;
    }

    /**
     * Handles movie card click to open detail view.
     */
    private void onCardClicked(MouseEvent event) {
        if (movie != null) {
            openMovieDetailView();
        }
    }

    /**
     * Opens the movie detail view in a new window.
     */
    private void openMovieDetailView() {
        try {
            FXMLLoader loader = new FXMLLoader(Main.class.getResource("view/MovieDetailView.fxml"));
            Parent root = loader.load();

            // Get the controller and set the movie
            MovieDetailViewController controller = loader.getController();
            controller.setMovie(movie);

            // Create and configure the stage
            Stage stage = new Stage();
            stage.setTitle(movie.getTitle() + " - Movie Details");
            stage.initModality(Modality.APPLICATION_MODAL);
            stage.setScene(new Scene(root));
            stage.setResizable(true);
            stage.setMinWidth(800);
            stage.setMinHeight(600);

            // Show the window
            stage.show();

            Logger.info("Opened movie detail view for: {}", movie.getTitle());
        } catch (IOException e) {
            Logger.error(e, "Failed to open movie detail view for: {}", movie.getTitle());
        }
    }

    /**
     * Sets the width of the movie card.
     *
     * @param width the desired width for the card
     */
    public void setCardWidth(double width) {
        if (movieCard != null) {
            movieCard.setPrefWidth(width);
            movieCard.setMaxWidth(width);

            // Also update the poster image width to maintain aspect ratio
            if (moviePosterImage != null) {
                moviePosterImage.setFitWidth(width);
            }
        }
    }

    /**
     * Gets the current movie.
     *
     * @return the current movie
     */
    public Movie getMovie() {
        return movie;
    }
}
