package oop2.project.controller;

import javafx.animation.KeyFrame;
import javafx.animation.Timeline;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.util.Duration;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.control.*;
import javafx.scene.layout.FlowPane;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import oop2.project.Main;
import oop2.project.config.AppConfig;
import oop2.project.model.Language;
import oop2.project.model.Movie;
import oop2.project.model.MovieSearchResult;
import oop2.project.model.MovieSearcherState;
import oop2.project.model.SortOption;
import oop2.project.model.TmdbQuery;
import oop2.project.service.MovieSearcherStateService;
import oop2.project.service.TmdbService;
import oop2.project.util.RatingSliderUtil;
import org.tinylog.Logger;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import java.util.stream.Stream;

/**
 * Controller for the movie searcher view.
 * Handles presentation logic for movie discovery and display.
 */
public class MovieSearcherController {

    // FXML UI Components
    @FXML
    private TextField searchField;

    @FXML
    private ComboBox<Language> languageFilterCombo;

    @FXML
    private Slider minRatingSlider;

    @FXML
    private Label ratingLabel;

    @FXML
    private CheckBox adultContentFilterCheckbox;

    @FXML
    private ComboBox<SortOption> sortByCombo;

    @FXML
    private FlowPane movieGridContainer;



    @FXML
    private StackPane contentArea;

    @FXML
    private VBox loadingIndicator;

    @FXML
    private VBox emptyStateContainer;

    @FXML
    private VBox errorStateContainer;

    @FXML
    private Label errorMessageLabel;

    @FXML
    private Button retryButton;



    // Pagination controls
    @FXML
    private Button firstPageButton;

    @FXML
    private Button prevPageButton;

    @FXML
    private Button nextPageButton;

    @FXML
    private Button lastPageButton;

    @FXML
    private Label pageInfoLabel;

    // Domain service (injected, not static)
    private final TmdbService tmdbService;
    private final AppConfig config;
    private final MovieSearcherStateService stateService;

    // State management
    private MovieSearcherState currentState;
    private CompletableFuture<MovieSearchResult> currentSearchTask;

    // Debounce mechanism for rating slider
    private static final double RATING_SLIDER_DEBOUNCE_DELAY_MS = 500.0;
    private Timeline ratingSliderDebounceTimer;

    /**
     * Constructor with dependency injection.
     */
    public MovieSearcherController() {
        this.tmdbService = new TmdbService();
        this.config = AppConfig.getInstance();
        this.stateService = MovieSearcherStateService.getInstance();
    }

    @FXML
    public void initialize() {
        // Restore state from service
        currentState = stateService.restoreState();

        setupUIComponents();
        setupEventHandlers();

        // Restore UI state and perform search if needed
        restoreUIFromState();
    }

    /**
     * Sets up initial UI component states.
     */
    private void setupUIComponents() {
        // Setup language combo box
        languageFilterCombo.getItems().addAll(Language.values());

        // Setup rating slider
        RatingSliderUtil.setupRatingSlider(minRatingSlider, ratingLabel);

        // Setup sort combo box
        sortByCombo.getItems().addAll(SortOption.values());

        // Setup responsive layout listener
        setupResponsiveLayout();

        // Initially show loading state
        showLoadingState();
    }

    /**
     * Sets up responsive layout that adjusts card sizes when window is resized.
     */
    private void setupResponsiveLayout() {
        // Add listener to container width changes to recalculate card sizes
        if (movieGridContainer != null && movieGridContainer.getParent() != null) {
            movieGridContainer.getParent().boundsInLocalProperty().addListener((obs, oldBounds, newBounds) -> {
                // Only refresh if we have movies displayed and the width actually changed
                if (currentState.getLastSearchResult() != null && !currentState.getLastSearchResult().getResults().isEmpty()
                    && oldBounds.getWidth() != newBounds.getWidth()) {
                    Platform.runLater(() -> displayMovies(currentState.getLastSearchResult().getResults()));
                }
            });
        }
    }

    /**
     * Sets up event handlers for UI interactions.
     */
    private void setupEventHandlers() {
        // Search on Enter key in search field
        searchField.setOnAction(e -> performSearch());



        // Retry button
        retryButton.setOnAction(e -> performSearch());

        // Pagination buttons
        firstPageButton.setOnAction(e -> navigateToPage(1));
        prevPageButton.setOnAction(e -> navigateToPage(currentState.getCurrentPage() - 1));
        nextPageButton.setOnAction(e -> navigateToPage(currentState.getCurrentPage() + 1));
        lastPageButton.setOnAction(e -> {
            if (currentState.getLastSearchResult() != null) {
                navigateToPage(currentState.getLastSearchResult().getTotalPages());
            }
        });

        // Auto-search on filter changes
        languageFilterCombo.setOnAction(e -> performSearch());

        // Debounced search for rating slider to prevent API spam
        minRatingSlider.valueProperty().addListener((obs, oldVal, newVal) -> {
            if (!oldVal.equals(newVal)) {
                debouncedRatingSearch();
            }
        });

        adultContentFilterCheckbox.setOnAction(e -> performSearch());
        sortByCombo.setOnAction(e -> performSearch());
    }

    /**
     * Performs a debounced search when the rating slider value changes.
     * This prevents excessive API calls when the user is dragging the slider.
     */
    private void debouncedRatingSearch() {
        // Cancel any existing debounce timer
        if (ratingSliderDebounceTimer != null) {
            ratingSliderDebounceTimer.stop();
        }

        // Create a new timer that will trigger the search after the delay
        ratingSliderDebounceTimer = new Timeline(new KeyFrame(
            Duration.millis(RATING_SLIDER_DEBOUNCE_DELAY_MS),
            e -> performSearch()
        ));

        // Start the timer
        ratingSliderDebounceTimer.play();
    }

    /**
     * Performs initial search when the view loads.
     */
    private void performInitialSearch() {
        performSearch();
    }

    /**
     * Performs a movie search based on current UI filter values.
     */
    private void performSearch() {
        performSearchForPage(1);
    }

    /**
     * Performs a movie search for a specific page.
     *
     * @param page the page number to search
     */
    private void performSearchForPage(int page) {
        // Cancel any ongoing search
        if (currentSearchTask != null && !currentSearchTask.isDone()) {
            currentSearchTask.cancel(true);
        }

        // Update state
        currentState.setCurrentPage(page);
        currentState.setCurrentViewState(MovieSearcherState.ViewState.LOADING);
        showLoadingState();

        // Build query from current state
        TmdbQuery query = buildQueryFromState();

        // Perform async search
        currentSearchTask = tmdbService.discoverMovies(query)
                .thenApply(result -> {
                    Platform.runLater(() -> handleSearchSuccess(result));
                    return result;
                })
                .exceptionally(throwable -> {
                    Platform.runLater(() -> handleSearchError(throwable));
                    return null;
                });
    }

    /**
     * Builds a TmdbQuery from current state.
     *
     * @return configured TmdbQuery
     */
    private TmdbQuery buildQueryFromState() {
        // Update state from UI before building query
        updateStateFromUI();

        // Use the state's toTmdbQuery method
        return currentState.toTmdbQuery();
    }

    /**
     * Updates the current state from UI component values.
     */
    private void updateStateFromUI() {
        currentState.setSearchText(searchField.getText());
        currentState.setSelectedLanguage(languageFilterCombo.getValue());
        currentState.setMinimumRating(RatingSliderUtil.getSliderValue(minRatingSlider));
        currentState.setIncludeAdultContent(adultContentFilterCheckbox.isSelected());
        currentState.setSelectedSortOption(sortByCombo.getValue());
    }

    /**
     * Handles successful search results.
     *
     * @param result the search result
     */
    private void handleSearchSuccess(MovieSearchResult result) {
        // Update state with results
        currentState.setLastSearchResult(result);
        currentState.setHasPerformedSearch(true);

        if (result.getResults().isEmpty()) {
            currentState.setCurrentViewState(MovieSearcherState.ViewState.EMPTY);
            showEmptyState();
        } else {
            currentState.setCurrentViewState(MovieSearcherState.ViewState.CONTENT);
            displayMovies(result.getResults());
            updatePaginationControls();
            showContentState();
        }

        // Save state to service
        stateService.saveState(currentState);
    }

    /**
     * Handles search errors with proper exception handling.
     *
     * @param throwable the error that occurred
     */
    private void handleSearchError(Throwable throwable) {
        Logger.error(throwable, "Error performing movie search");

        // Update state
        currentState.setCurrentViewState(MovieSearcherState.ViewState.ERROR);

        String errorMessage = "Unable to load movies. Please check your internet connection and try again.";
        if (throwable.getCause() != null) {
            String cause = throwable.getCause().getCause().getMessage();
            if (cause != null && cause.contains("401")) {
                errorMessage = "Invalid API key. Please check your TMDB API key in settings.";
            } else if (cause != null && cause.contains("timeout")) {
                errorMessage = "Request timed out. Please try again.";
            }
        }

        errorMessageLabel.setText(errorMessage);
        showErrorState();

        // Save state to service
        stateService.saveState(currentState);
    }

    /**
     * Displays movies in the grid container using streams for processing.
     *
     * @param movies the list of movies to display
     */
    private void displayMovies(List<Movie> movies) {
        movieGridContainer.getChildren().clear();

        // Calculate optimal card width based on available space
        double optimalCardWidth = calculateOptimalCardWidth();

        // Use streams for declarative data processing
        movies.stream()
                .filter(movie -> movie != null) // Filter out null movies
                .map(movie -> createMovieCard(movie, optimalCardWidth))
                .filter(node -> node != null) // Filter out failed card creations
                .forEach(movieGridContainer.getChildren()::add);
    }

    /**
     * Creates a movie card node for the given movie with specified width.
     *
     * @param movie the movie to create a card for
     * @param cardWidth the desired width for the card
     * @return the movie card node or null if creation fails
     */
    private Node createMovieCard(Movie movie, double cardWidth) {
        try {
            FXMLLoader loader = new FXMLLoader(Main.class.getResource("view/MovieCard.fxml"));
            Node movieCard = loader.load();

            // Get controller and set movie data
            MovieCardController controller = loader.getController();
            if (controller != null) {
                controller.setMovie(movie);
                controller.setCardWidth(cardWidth);
            }

            return movieCard;
        } catch (IOException e) {
            Logger.error(e, "Failed to create movie card for movie: {}", movie.getTitle());
            return null;
        }
    }

    /**
     * Calculates the optimal card width based on available space.
     * This ensures cards are evenly distributed across the container width.
     *
     * @return the optimal card width
     */
    private double calculateOptimalCardWidth() {
        if (movieGridContainer == null || movieGridContainer.getParent() == null) {
            return 280.0; // Default width
        }

        // Get the available width (container width minus padding)
        double containerWidth = movieGridContainer.getParent().getBoundsInLocal().getWidth();
        double padding = 48.0; // Account for container padding (24px on each side)
        double availableWidth = containerWidth - padding;

        // Define constraints
        double minCardWidth = 240.0;
        double maxCardWidth = 320.0;
        double gap = 20.0;

        if (availableWidth <= 0) {
            return 280.0; // Default if we can't calculate
        }

        // Calculate how many cards can fit with different widths
        for (double cardWidth = maxCardWidth; cardWidth >= minCardWidth; cardWidth -= 10) {
            int cardsPerRow = (int) ((availableWidth + gap) / (cardWidth + gap));
            if (cardsPerRow >= 1) {
                // Calculate the actual width that would evenly distribute the cards
                double totalGapWidth = (cardsPerRow - 1) * gap;
                double actualCardWidth = (availableWidth - totalGapWidth) / cardsPerRow;

                // Ensure the calculated width is within our bounds
                if (actualCardWidth >= minCardWidth && actualCardWidth <= maxCardWidth) {
                    return actualCardWidth;
                }
            }
        }

        return 280.0; // Fallback to default
    }

    /**
     * Navigates to a specific page.
     *
     * @param page the page number to navigate to
     */
    private void navigateToPage(int page) {
        if (currentState.getLastSearchResult() == null) {
            return;
        }

        if (page < 1 || page > currentState.getLastSearchResult().getTotalPages()) {
            return;
        }

        performSearchForPage(page);
    }

    /**
     * Updates pagination control states based on current search result.
     */
    private void updatePaginationControls() {
        if (currentState.getLastSearchResult() == null) {
            return;
        }

        boolean hasPrevious = currentState.getCurrentPage() > 1;
        boolean hasNext = currentState.getCurrentPage() < currentState.getLastSearchResult().getTotalPages();

        firstPageButton.setDisable(!hasPrevious);
        prevPageButton.setDisable(!hasPrevious);
        nextPageButton.setDisable(!hasNext);
        lastPageButton.setDisable(!hasNext);

        // Update page number display
        updatePageNumberDisplay();
    }

    /**
     * Updates the page number display with current page info.
     */
    private void updatePageNumberDisplay() {
        if (currentState.getLastSearchResult() == null || pageInfoLabel == null) {
            return;
        }

        int totalPages = currentState.getLastSearchResult().getTotalPages();
        int currentPageNum = currentState.getCurrentPage();

        // Update page info label
        pageInfoLabel.setText(String.format("Page %d of %d", currentPageNum, totalPages));
    }



    // State management methods

    /**
     * Shows the loading state.
     */
    private void showLoadingState() {
        setAllStatesVisible(false);
        if (loadingIndicator != null) {
            loadingIndicator.setVisible(true);
        }
    }

    /**
     * Shows the content state (movies displayed).
     */
    private void showContentState() {
        setAllStatesVisible(false);
        movieGridContainer.setVisible(true);
    }

    /**
     * Shows the empty state (no movies found).
     */
    private void showEmptyState() {
        setAllStatesVisible(false);
        emptyStateContainer.setVisible(true);
    }

    /**
     * Shows the error state.
     */
    private void showErrorState() {
        setAllStatesVisible(false);
        errorStateContainer.setVisible(true);
    }

    /**
     * Sets visibility of all state containers.
     *
     * @param visible the visibility state
     */
    private void setAllStatesVisible(boolean visible) {
        Stream.of(loadingIndicator, emptyStateContainer, errorStateContainer, movieGridContainer)
                .filter(container -> container != null)
                .forEach(container -> container.setVisible(visible));
    }

    /**
     * Resets the search form to default values.
     */
    @FXML
    private void resetSearch() {
        // Reset state to initial values
        currentState.resetToInitial();

        // Reset UI to match state
        searchField.clear();
        languageFilterCombo.setValue(config.getLanguage());
        minRatingSlider.setValue(-1);
        adultContentFilterCheckbox.setSelected(config.allowAdultContent());
        sortByCombo.setValue(SortOption.getDefault());

        // Update state from UI and perform search
        updateStateFromUI();
        performSearch();
    }

    /**
     * Restores UI components from the current state.
     */
    private void restoreUIFromState() {
        // Set UI components from state
        searchField.setText(currentState.getSearchText());

        if (currentState.getSelectedLanguage() != null) {
            languageFilterCombo.setValue(currentState.getSelectedLanguage());
        } else {
            languageFilterCombo.setValue(config.getLanguage());
        }

        minRatingSlider.setValue(currentState.getMinimumRating());
        adultContentFilterCheckbox.setSelected(currentState.isIncludeAdultContent());

        if (currentState.getSelectedSortOption() != null) {
            sortByCombo.setValue(currentState.getSelectedSortOption());
        } else {
            sortByCombo.setValue(SortOption.getDefault());
        }

        // Restore view state and results if available
        if (currentState.hasPerformedSearch() && currentState.getLastSearchResult() != null) {
            switch (currentState.getCurrentViewState()) {
                case CONTENT:
                    displayMovies(currentState.getLastSearchResult().getResults());
                    updatePaginationControls();
                    showContentState();
                    break;
                case EMPTY:
                    showEmptyState();
                    break;
                case ERROR:
                    showErrorState();
                    break;
                default:
                    performInitialSearch();
                    break;
            }
        } else {
            performInitialSearch();
        }
    }

    /**
     * Saves the current state when navigating away from this view.
     * This method should be called by the MainViewController before switching views.
     */
    public void saveCurrentState() {
        if (currentState != null) {
            // Update state from current UI values
            updateStateFromUI();

            // Save to state service
            stateService.saveState(currentState);

            Logger.debug("Saved movie searcher state on navigation: {}", currentState);
        }
    }
}
